<?php
/*
Template Name: Contact Page
*/
get_header(); ?>

<style>
:root {
    --fc-black: #050608;
    --fc-blue: #0049a3;
    --fc-orange: #ff6b35;
    --fc-text-primary: #ffffff;
    --fc-text-secondary: #d1d5db;
    --fc-bg-card: rgba(255, 255, 255, 0.05);
    --fc-gradient-primary: linear-gradient(135deg, #0ea5e9, #0049a3);
}

.contact-container {
    min-height: 100vh;
    background: var(--fc-black);
    color: var(--fc-text-primary);
    padding: 2rem 0;
}

.contact-card {
    background: rgba(31, 41, 55, 0.5);
    border: 1px solid rgba(6, 182, 212, 0.2);
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
}

.form-input, .form-textarea, .form-select {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #374151;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    width: 100%;
    transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #0ea5e9, #0049a3);
    color: white;
    font-weight: bold;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0284c7, #003875);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-outline:hover {
    border-color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
    color: #0ea5e9;
}

.contact-method {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(6, 182, 212, 0.2);
    background: rgba(14, 165, 233, 0.1);
    transition: all 0.3s ease;
}

.contact-method:hover {
    border-color: rgba(6, 182, 212, 0.5);
}

.faq-category {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #374151;
    transition: all 0.3s ease;
    cursor: pointer;
}

.faq-category:hover {
    border-color: rgba(6, 182, 212, 0.5);
}

.gradient-text {
    background: linear-gradient(135deg, #0ea5e9, #0049a3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.success-message {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Minimal WPForms styling - only labels and title */
.wpforms-title {
    display: none !important;
}

.wpforms-head-container {
    display: none !important;
}

.wpforms-field-label {
    color: white !important;
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .contact-card {
        padding: 1.5rem !important;
    }
}
</style>

<!-- Contact Page Content -->
<main class="contact-container">
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">

        <!-- Page Header -->
        <div style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: clamp(2.5rem, 5vw, 4rem); font-weight: bold; margin-bottom: 1rem;" class="gradient-text">
                CONTACT US
            </h1>
            <p style="font-size: 1.25rem; color: var(--fc-text-secondary); max-width: 600px; margin: 0 auto 1.5rem;">
                Get in touch with our support team via email - we're here to help!
            </p>
            <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; color: #0ea5e9;">
                <span style="font-size: 1.25rem;">⏰</span>
                <span style="font-weight: 600;">Average response time: 2 hours</span>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="contact-grid" style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; max-width: 1200px; margin: 0 auto;">

            <!-- Contact Form -->
            <div class="contact-card" style="padding: 2rem;">
                <div style="margin-bottom: 1rem;">
                    <h2 style="color: #0ea5e9; font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.25rem;">📧</span>
                        Send us a Message
                    </h2>
                </div>

                <!-- WPForms Integration -->
                <div style="color: white;">
                    <?php
                    // Display WPForms contact form ID 131
                    if (function_exists('wpforms_display')) {
                        echo wpforms_display(131, true, true);
                    } else {
                        // Fallback message if WPForms is not active
                        echo '<div style="padding: 2rem; text-align: center; background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 0.5rem;">';
                        echo '<p style="color: #fca5a5; margin: 0;">WPForms plugin is not active. Please install and activate WPForms to display the contact form.</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">

                <!-- Contact Methods -->
                <div class="contact-card" style="padding: 1.5rem;">
                    <h3 style="color: #0ea5e9; font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem;">Get in Touch</h3>

                    <div class="contact-method">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <span style="font-size: 1.25rem; color: #0ea5e9; margin-top: 0.25rem;">📧</span>
                            <div style="flex: 1;">
                                <h4 style="font-weight: 600; color: #0ea5e9; margin-bottom: 0.25rem;">Email Support</h4>
                                <p style="font-size: 0.875rem; color: var(--fc-text-secondary); margin-bottom: 0.5rem;">Get help via email</p>
                                <p style="color: white; font-weight: 500;"><EMAIL></p>
                                <p style="font-size: 0.75rem; color: #9ca3af; margin-top: 0.25rem;">Response within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Company Info -->
                <div class="contact-card" style="padding: 1.5rem;">
                    <h3 style="color: #0ea5e9; font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem;">Company Information</h3>

                    <div style="color: var(--fc-text-secondary); line-height: 1.6;">
                        <p style="font-weight: 600; color: white; margin-bottom: 0.5rem;">FILM COLLECTABLES COMPETITIONS LIMITED</p>
                        <p style="margin-bottom: 0.25rem;">Ground Floor, Gallery Building</p>
                        <p style="margin-bottom: 0.25rem;">65-69 Dublin Rd</p>
                        <p style="margin-bottom: 0.25rem;">Belfast, BT2 7HG</p>
                        <p style="margin-bottom: 1rem;">United Kingdom</p>
                        <p style="font-size: 0.875rem;"><span style="color: #0ea5e9;">Company No:</span> NI730025</p>
                    </div>
                </div>

            </div>
        </div>

    </div>


</main>

<script>
// Mobile responsive adjustments
function handleResize() {
    const grid = document.querySelector('.contact-grid');
    if (window.innerWidth <= 768) {
        grid.style.gridTemplateColumns = '1fr';
    } else {
        grid.style.gridTemplateColumns = '2fr 1fr';
    }
}

window.addEventListener('resize', handleResize);
document.addEventListener('DOMContentLoaded', handleResize);
</script>

<?php get_footer(); ?>
