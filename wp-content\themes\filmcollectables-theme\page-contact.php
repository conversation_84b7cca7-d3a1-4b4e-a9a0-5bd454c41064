<?php
/*
Template Name: Contact Page
*/
get_header(); ?>

<style>
:root {
    --fc-black: #050608;
    --fc-blue: #0049a3;
    --fc-orange: #ff6b35;
    --fc-text-primary: #ffffff;
    --fc-text-secondary: #d1d5db;
    --fc-bg-card: rgba(255, 255, 255, 0.05);
    --fc-gradient-primary: linear-gradient(135deg, #0ea5e9, #0049a3);
}

.contact-container {
    min-height: 100vh;
    background: var(--fc-black);
    color: var(--fc-text-primary);
    padding: 2rem 0;
}

.contact-card {
    background: rgba(31, 41, 55, 0.5);
    border: 1px solid rgba(6, 182, 212, 0.2);
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
}

.form-input, .form-textarea, .form-select {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #374151;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    width: 100%;
    transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #0ea5e9, #0049a3);
    color: white;
    font-weight: bold;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0284c7, #003875);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-outline:hover {
    border-color: #0ea5e9;
    background: rgba(14, 165, 233, 0.1);
    color: #0ea5e9;
}

.contact-method {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(6, 182, 212, 0.2);
    background: rgba(14, 165, 233, 0.1);
    transition: all 0.3s ease;
}

.contact-method:hover {
    border-color: rgba(6, 182, 212, 0.5);
}

.faq-category {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #374151;
    transition: all 0.3s ease;
    cursor: pointer;
}

.faq-category:hover {
    border-color: rgba(6, 182, 212, 0.5);
}

.gradient-text {
    background: linear-gradient(135deg, #0ea5e9, #0049a3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.success-message {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* WPForms Dark Theme Styling */
.wpforms-container {
    color: white !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
}

.wpforms-form {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Hide WPForms title since we have our own */
.wpforms-title {
    display: none !important;
}

.wpforms-description {
    display: none !important;
}

.wpforms-head-container {
    display: none !important;
}

.wpforms-form-header {
    display: none !important;
}

/* Remove extra spacing at top of form */
.wpforms-container-full {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Reduce spacing between heading and form */
.contact-card h2 {
    margin-bottom: 1rem !important;
}

.contact-card > div:first-child {
    margin-bottom: 1rem !important;
}

/* Ensure form starts immediately after heading */
.wpforms-container {
    margin-top: 0 !important;
}

.wpforms-field:first-child {
    margin-top: 0 !important;
}

.wpforms-field-label {
    color: var(--fc-text-secondary) !important;
    font-weight: 500 !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
    font-size: 0.875rem !important;
}

.wpforms-field input[type="text"],
.wpforms-field input[type="email"],
.wpforms-field input[type="tel"],
.wpforms-field select,
.wpforms-field textarea {
    background: rgba(0, 0, 0, 0.5) !important;
    border: 1px solid #374151 !important;
    color: white !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.375rem !important;
    transition: border-color 0.3s ease !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    font-size: 1rem !important;
    margin: 0 !important;
}

.wpforms-field input[type="text"]:focus,
.wpforms-field input[type="email"]:focus,
.wpforms-field input[type="tel"]:focus,
.wpforms-field select:focus,
.wpforms-field textarea:focus {
    outline: none !important;
    border-color: #0ea5e9 !important;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1) !important;
}

.wpforms-field textarea {
    min-height: 120px !important;
    resize: vertical !important;
}

.wpforms-submit-container {
    margin-top: 1rem !important;
}

.wpforms-submit-container button {
    background: linear-gradient(135deg, #0ea5e9, #0049a3) !important;
    color: white !important;
    font-weight: bold !important;
    padding: 1rem 1.5rem !important;
    border-radius: 0.375rem !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    font-size: 1rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
}

.wpforms-submit-container button:hover {
    background: linear-gradient(135deg, #0284c7, #003875) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3) !important;
}

.wpforms-field {
    margin-bottom: 1.5rem !important;
    width: 100% !important;
    display: block !important;
    clear: both !important;
}

.wpforms-field-row {
    display: flex !important;
    gap: 1rem !important;
    width: 100% !important;
    flex-wrap: wrap !important;
}

.wpforms-field-row .wpforms-field {
    margin-bottom: 1.5rem !important;
    flex: 1 !important;
    min-width: 0 !important;
}

/* Fix for WPForms Name Field (First/Last Name) */
.wpforms-field-name {
    width: 100% !important;
    display: block !important;
    margin-bottom: 1.5rem !important;
}

.wpforms-field-name .wpforms-field-row {
    display: flex !important;
    gap: 1rem !important;
    width: 100% !important;
    margin: 0 !important;
    flex-wrap: nowrap !important;
}

.wpforms-field-name .wpforms-field-row-block {
    flex: 1 !important;
    width: 50% !important;
    margin: 0 !important;
    float: none !important;
    display: block !important;
}

.wpforms-field-name input {
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}

/* WPForms one-half classes - Override for name fields */
.wpforms-field-name .wpforms-one-half {
    width: 50% !important;
    float: none !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
    flex: 1 !important;
}

/* General one-half classes for other fields */
.wpforms-one-half:not(.wpforms-field-name .wpforms-one-half) {
    width: 48% !important;
    float: left !important;
    margin-right: 4% !important;
    box-sizing: border-box !important;
}

.wpforms-one-half.wpforms-last:not(.wpforms-field-name .wpforms-one-half) {
    margin-right: 0 !important;
}

/* Fix for sublabels in name fields */
.wpforms-field-sublabel {
    color: var(--fc-text-secondary) !important;
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
    display: block !important;
}

/* Clear floats */
.wpforms-clear::after,
.wpforms-field::after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

.wpforms-confirmation-container-full {
    background: rgba(16, 185, 129, 0.1) !important;
    border: 1px solid rgba(16, 185, 129, 0.3) !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    color: #10b981 !important;
    text-align: center !important;
}

/* Fix for dropdown styling */
.wpforms-field select {
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1rem !important;
    padding-right: 2.5rem !important;
}

/* Error styling */
.wpforms-error {
    color: #ef4444 !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

.wpforms-field.wpforms-has-error input,
.wpforms-field.wpforms-has-error select,
.wpforms-field.wpforms-has-error textarea {
    border-color: #ef4444 !important;
}

/* Additional fixes for field alignment */
.wpforms-field-container {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Force proper alignment for all field types */
.wpforms-field > div {
    width: 100% !important;
    margin: 0 !important;
}

.wpforms-field input,
.wpforms-field select,
.wpforms-field textarea {
    max-width: 100% !important;
    min-width: 0 !important;
    display: block !important;
}

/* Ensure full width for single fields */
.wpforms-field:not(.wpforms-field-name) input,
.wpforms-field:not(.wpforms-field-name) select,
.wpforms-field:not(.wpforms-field-name) textarea {
    width: 100% !important;
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .contact-card {
        padding: 1.5rem !important;
    }

    .wpforms-field input[type="text"],
    .wpforms-field input[type="email"],
    .wpforms-field input[type="tel"],
    .wpforms-field select,
    .wpforms-field textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
    }

    /* Stack all fields on mobile */
    .wpforms-field-row {
        flex-direction: column !important;
        gap: 0 !important;
    }

    .wpforms-field-row .wpforms-field {
        width: 100% !important;
        margin-bottom: 1.5rem !important;
    }

    /* Stack name fields on mobile */
    .wpforms-field-name .wpforms-field-row {
        flex-direction: column !important;
        gap: 0 !important;
    }

    .wpforms-field-name .wpforms-field-row-block,
    .wpforms-field-name .wpforms-one-half {
        width: 100% !important;
        margin-bottom: 1rem !important;
        flex: none !important;
    }

    .wpforms-one-half {
        width: 100% !important;
        float: none !important;
        margin-right: 0 !important;
        margin-bottom: 1rem !important;
    }
}
</style>

<!-- Contact Page Content -->
<main class="contact-container">
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">

        <!-- Page Header -->
        <div style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: clamp(2.5rem, 5vw, 4rem); font-weight: bold; margin-bottom: 1rem;" class="gradient-text">
                CONTACT US
            </h1>
            <p style="font-size: 1.25rem; color: var(--fc-text-secondary); max-width: 600px; margin: 0 auto 1.5rem;">
                Get in touch with our support team via email - we're here to help!
            </p>
            <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; color: #0ea5e9;">
                <span style="font-size: 1.25rem;">⏰</span>
                <span style="font-weight: 600;">Average response time: 2 hours</span>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="contact-grid" style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; max-width: 1200px; margin: 0 auto;">

            <!-- Contact Form -->
            <div class="contact-card" style="padding: 2rem;">
                <div style="margin-bottom: 1rem;">
                    <h2 style="color: #0ea5e9; font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.25rem;">📧</span>
                        Send us a Message
                    </h2>
                </div>

                <!-- WPForms Integration -->
                <div style="color: white;">
                    <?php
                    // Display WPForms contact form ID 131
                    if (function_exists('wpforms_display')) {
                        echo wpforms_display(131, true, true);
                    } else {
                        // Fallback message if WPForms is not active
                        echo '<div style="padding: 2rem; text-align: center; background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 0.5rem;">';
                        echo '<p style="color: #fca5a5; margin: 0;">WPForms plugin is not active. Please install and activate WPForms to display the contact form.</p>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div style="display: flex; flex-direction: column; gap: 1.5rem;">

                <!-- Contact Methods -->
                <div class="contact-card" style="padding: 1.5rem;">
                    <h3 style="color: #0ea5e9; font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem;">Get in Touch</h3>

                    <div class="contact-method">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <span style="font-size: 1.25rem; color: #0ea5e9; margin-top: 0.25rem;">📧</span>
                            <div style="flex: 1;">
                                <h4 style="font-weight: 600; color: #0ea5e9; margin-bottom: 0.25rem;">Email Support</h4>
                                <p style="font-size: 0.875rem; color: var(--fc-text-secondary); margin-bottom: 0.5rem;">Get help via email</p>
                                <p style="color: white; font-weight: 500;"><EMAIL></p>
                                <p style="font-size: 0.75rem; color: #9ca3af; margin-top: 0.25rem;">Response within 24 hours</p>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Company Info -->
                <div class="contact-card" style="padding: 1.5rem;">
                    <h3 style="color: #0ea5e9; font-size: 1.25rem; font-weight: bold; margin-bottom: 1rem;">Company Information</h3>

                    <div style="color: var(--fc-text-secondary); line-height: 1.6;">
                        <p style="font-weight: 600; color: white; margin-bottom: 0.5rem;">FILM COLLECTABLES COMPETITIONS LIMITED</p>
                        <p style="margin-bottom: 0.25rem;">Ground Floor, Gallery Building</p>
                        <p style="margin-bottom: 0.25rem;">65-69 Dublin Rd</p>
                        <p style="margin-bottom: 0.25rem;">Belfast, BT2 7HG</p>
                        <p style="margin-bottom: 1rem;">United Kingdom</p>
                        <p style="font-size: 0.875rem;"><span style="color: #0ea5e9;">Company No:</span> NI730025</p>
                    </div>
                </div>

            </div>
        </div>

    </div>


</main>

<script>
// Mobile responsive adjustments
function handleResize() {
    const grid = document.querySelector('.contact-grid');
    if (window.innerWidth <= 768) {
        grid.style.gridTemplateColumns = '1fr';
    } else {
        grid.style.gridTemplateColumns = '2fr 1fr';
    }
}

window.addEventListener('resize', handleResize);
document.addEventListener('DOMContentLoaded', handleResize);
</script>

<?php get_footer(); ?>
