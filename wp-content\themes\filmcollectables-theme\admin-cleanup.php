<?php
/**
 * Database Cleanup Tool for Film Collectables
 * This file provides functionality to clear all test data from the database
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu for database cleanup
 */
function fc_add_cleanup_admin_menu() {
    add_management_page(
        'Database Cleanup',
        'Database Cleanup',
        'manage_options',
        'fc-database-cleanup',
        'fc_database_cleanup_page'
    );
}
add_action('admin_menu', 'fc_add_cleanup_admin_menu');

/**
 * Database cleanup admin page
 */
function fc_database_cleanup_page() {
    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    // Check if cleanup is allowed on this environment
    $cleanup_allowed = fc_is_cleanup_allowed();

    // Handle form submission
    if (isset($_POST['fc_cleanup_confirm']) && wp_verify_nonce($_POST['fc_cleanup_nonce'], 'fc_cleanup_action')) {
        if ($_POST['fc_cleanup_confirm'] === 'DELETE_ALL_TEST_DATA') {
            if ($cleanup_allowed) {
                $results = fc_perform_database_cleanup();
                if ($results['success']) {
                    echo '<div class="notice notice-success"><p><strong>Cleanup completed!</strong> ' . $results['message'] . '</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p><strong>Cleanup failed:</strong> ' . $results['message'] . '</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p><strong>Cleanup is disabled on production environments.</strong> For safety, this tool only works on development/localhost environments.</p></div>';
            }
        } else {
            echo '<div class="notice notice-error"><p><strong>Confirmation text incorrect. No data was deleted.</strong></p></div>';
        }
    }

    // Handle wallet cleanup
    if (isset($_POST['fc_wallet_cleanup']) && wp_verify_nonce($_POST['fc_wallet_nonce'], 'fc_wallet_cleanup_action')) {
        $wallet_results = fc_cleanup_wallet_inconsistencies();
        if ($wallet_results['success']) {
            echo '<div class="notice notice-success"><p><strong>Wallet cleanup completed!</strong> ' . $wallet_results['message'] . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p><strong>Wallet cleanup failed:</strong> ' . $wallet_results['message'] . '</p></div>';
        }
    }

    // Handle cache clear
    if (isset($_POST['fc_cache_clear']) && wp_verify_nonce($_POST['fc_cache_nonce'], 'fc_cache_clear_action')) {
        fc_clear_all_caches();
        echo '<div class="notice notice-success"><p><strong>All caches cleared!</strong> Please refresh the page to see updated wallet balance.</p></div>';
    }

    // Handle force refresh
    if (isset($_POST['fc_force_refresh']) && wp_verify_nonce($_POST['fc_refresh_nonce'], 'fc_force_refresh_action')) {
        fc_force_wallet_refresh();
        echo '<div class="notice notice-success"><p><strong>Wallet display force refreshed!</strong> The navigation should now show the correct balance.</p></div>';
    }

    // Get current data counts
    $counts = fc_get_data_counts();
    ?>
    <div class="wrap">
        <h1>🗑️ Database Cleanup Tool</h1>
        
        <?php if (!$cleanup_allowed): ?>
            <div class="notice notice-error">
                <p><strong>🚫 CLEANUP DISABLED:</strong> For safety, database cleanup is disabled on production environments. This tool only works on development/localhost environments.</p>
                <p><strong>Current site URL:</strong> <?php echo get_site_url(); ?></p>
            </div>
        <?php else: ?>
            <div class="notice notice-warning">
                <p><strong>⚠️ WARNING:</strong> This tool will permanently delete ALL test data from your database. This action cannot be undone!</p>
            </div>
        <?php endif; ?>

        <div class="card" style="max-width: 800px;">
            <h2>Current Database Contents</h2>
            <table class="widefat">
                <thead>
                    <tr>
                        <th>Data Type</th>
                        <th>Count</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>WooCommerce Products</strong></td>
                        <td><?php echo $counts['products']; ?></td>
                        <td>Competition products and variations</td>
                    </tr>
                    <tr>
                        <td><strong>WooCommerce Orders</strong></td>
                        <td><?php echo $counts['orders']; ?></td>
                        <td>Customer orders and transactions</td>
                    </tr>
                    <tr>
                        <td><strong>Competition Tickets</strong></td>
                        <td><?php echo $counts['tickets']; ?></td>
                        <td>Individual competition entries</td>
                    </tr>
                    <tr>
                        <td><strong>Instant Wins</strong></td>
                        <td><?php echo $counts['instant_wins']; ?></td>
                        <td>Instant win prizes and assignments</td>
                    </tr>
                    <tr>
                        <td><strong>Competition Winners</strong></td>
                        <td><?php echo $counts['winners']; ?></td>
                        <td>Selected competition winners</td>
                    </tr>
                    <tr>
                        <td><strong>User Accounts</strong></td>
                        <td><?php echo $counts['users']; ?></td>
                        <td>Customer accounts (excluding admin)</td>
                    </tr>
                    <tr>
                        <td><strong>User Wallet Credits</strong></td>
                        <td><?php echo $counts['wallet_credits']; ?></td>
                        <td>Site credit balances</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>💰 Wallet Balance Cleanup</h2>
            <p>If you're experiencing wallet balance inconsistencies (different amounts showing on different pages), use this tool to fix the issue:</p>

            <div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 15px 0; border-radius: 5px;">
                <h4 style="margin-top: 0;">🔧 What this fixes:</h4>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <li>Removes duplicate wallet balance entries in the database</li>
                    <li>Clears all wallet transaction history</li>
                    <li>Clears cached wallet data that might be stale</li>
                    <li>Ensures all pages show the same wallet balance</li>
                    <li>Preserves the correct wallet balance for each user</li>
                </ul>
            </div>

            <form method="post" style="margin: 20px 0;">
                <?php wp_nonce_field('fc_wallet_cleanup_action', 'fc_wallet_nonce'); ?>
                <button type="submit"
                        name="fc_wallet_cleanup"
                        value="1"
                        class="button button-primary"
                        onclick="return confirm('This will clean up wallet balance inconsistencies. Continue?');">
                    🔧 Fix Wallet Balance Inconsistencies
                </button>
            </form>

            <form method="post" style="margin: 20px 0;">
                <?php wp_nonce_field('fc_cache_clear_action', 'fc_cache_nonce'); ?>
                <button type="submit"
                        name="fc_cache_clear"
                        value="1"
                        class="button button-secondary">
                    🗑️ Clear All Caches Only
                </button>
                <p><small>Use this if wallet balance is still showing old values after cleanup.</small></p>
            </form>

            <form method="post" style="margin: 20px 0;">
                <?php wp_nonce_field('fc_force_refresh_action', 'fc_refresh_nonce'); ?>
                <button type="submit"
                        name="fc_force_refresh"
                        value="1"
                        class="button button-secondary">
                    🔄 Force Wallet Display Refresh
                </button>
                <p><small>Use this to force update the navigation bar wallet display.</small></p>
            </form>
        </div>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>🚨 Full Database Cleanup</h2>
            <p>The following data will be <strong>permanently deleted</strong>:</p>
            <ul style="list-style-type: disc; margin-left: 20px;">
                <li>All WooCommerce products (competitions)</li>
                <li>All WooCommerce orders and order items</li>
                <li>All competition tickets and entries</li>
                <li>All instant win data and assignments</li>
                <li>All competition winners</li>
                <li>All customer user accounts (admin accounts preserved)</li>
                <li>All user wallet credits and transactions</li>
                <li>All product images and attachments</li>
                <li>All related metadata and custom fields</li>
            </ul>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">
                <h3 style="margin-top: 0;">⚠️ Data That Will Be Preserved:</h3>
                <ul style="list-style-type: disc; margin-left: 20px;">
                    <li>Admin user accounts</li>
                    <li>WordPress pages and posts</li>
                    <li>Theme settings and customizations</li>
                    <li>Plugin settings and configurations</li>
                    <li>Terms & Conditions and Privacy Policy pages</li>
                </ul>
            </div>

            <?php if ($cleanup_allowed): ?>
                <form method="post" style="margin-top: 30px;">
                    <?php wp_nonce_field('fc_cleanup_action', 'fc_cleanup_nonce'); ?>

                    <h3>Confirmation Required</h3>
                    <p>To proceed with the cleanup, type <code>DELETE_ALL_TEST_DATA</code> in the box below:</p>

                    <input type="text"
                           name="fc_cleanup_confirm"
                           placeholder="Type: DELETE_ALL_TEST_DATA"
                           style="width: 300px; padding: 8px; font-family: monospace;"
                           required>

                    <br><br>

                    <button type="submit"
                            class="button button-primary button-large"
                            style="background: #dc3545; border-color: #dc3545;"
                            onclick="return confirm('Are you absolutely sure you want to delete ALL test data? This cannot be undone!');">
                        🗑️ Delete All Test Data
                    </button>
                </form>
            <?php else: ?>
                <div style="margin-top: 30px; background: #f8d7da; border: 1px solid #f5c2c7; padding: 15px; border-radius: 5px;">
                    <h3 style="color: #842029; margin-top: 0;">🚫 Cleanup Disabled</h3>
                    <p>Database cleanup is disabled on production environments for safety.</p>
                    <p>To use this feature:</p>
                    <ol>
                        <li>Set up a local development environment</li>
                        <li>Import your database to the local environment</li>
                        <li>Run the cleanup tool locally</li>
                        <li>Export the clean database back to production</li>
                    </ol>
                </div>
            <?php endif; ?>
        </div>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>📋 Recommended Steps After Cleanup</h2>
            <ol>
                <li>Create your first real competition product</li>
                <li>Test the competition entry process</li>
                <li>Configure payment settings for live transactions</li>
                <li>Update any hardcoded test data in theme files</li>
                <li>Set up proper backup procedures</li>
                <li>Configure email notifications for live environment</li>
            </ol>
        </div>
    </div>

    <style>
    .card {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .card h2 {
        margin-top: 0;
        color: #23282d;
    }
    .widefat th {
        background: #f1f1f1;
        font-weight: 600;
    }
    .widefat td, .widefat th {
        padding: 12px;
        border-bottom: 1px solid #ddd;
    }
    </style>
    <?php
}

/**
 * Get current data counts from database
 */
function fc_get_data_counts() {
    global $wpdb;
    
    $counts = array();
    
    // Count products
    $counts['products'] = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM {$wpdb->posts} 
        WHERE post_type = 'product' 
        AND post_status IN ('publish', 'draft', 'private')
    ");
    
    // Count orders (both legacy posts and HPOS)
    $legacy_orders = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE post_type IN ('shop_order', 'shop_order_refund')
    ") ?: 0;

    $hpos_orders = 0;
    if ($wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_orders'") == $wpdb->prefix . 'wc_orders') {
        $hpos_orders = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}wc_orders") ?: 0;
    }

    $counts['orders'] = $legacy_orders + $hpos_orders;

    // Count order items
    $counts['order_items'] = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->prefix}woocommerce_order_items
    ") ?: 0;
    
    // Count tickets
    $counts['tickets'] = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM {$wpdb->prefix}competition_tickets
    ");
    
    // Count instant wins (both tables)
    $instant_wins_1 = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}instant_wins") ?: 0;
    $instant_wins_2 = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}competition_instant_wins") ?: 0;
    $counts['instant_wins'] = $instant_wins_1 + $instant_wins_2;

    // Count winners (both table and posts)
    $winners_table_count = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->prefix}competition_winners
    ") ?: 0;

    $winner_posts_count = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->posts}
        WHERE post_type = 'winner'
    ") ?: 0;

    $counts['winners'] = $winners_table_count + $winner_posts_count;

    // Count users (excluding admin)
    $counts['users'] = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->users} u
        INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id
        WHERE um.meta_key = '{$wpdb->prefix}capabilities'
        AND um.meta_value NOT LIKE '%administrator%'
        AND um.meta_value NOT LIKE '%editor%'
    ");

    // Count wallet credits
    $counts['wallet_credits'] = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->usermeta}
        WHERE meta_key = 'wallet_balance'
        AND meta_value > 0
    ");

    // Count wallet transactions
    $counts['wallet_transactions'] = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->prefix}wallet_transactions
    ") ?: 0;
    
    return $counts;
}

/**
 * Check if cleanup should be allowed
 */
function fc_is_cleanup_allowed() {
    // Don't allow cleanup if this looks like a production site
    $site_url = get_site_url();

    // Block cleanup on live domains
    $production_indicators = array(
        'filmcollectables.com',
        'filmcollectables.co.uk',
        '.com',
        '.co.uk',
        '.org',
        '.net'
    );

    foreach ($production_indicators as $indicator) {
        if (strpos($site_url, $indicator) !== false && strpos($site_url, 'localhost') === false) {
            return false;
        }
    }

    // Allow cleanup on localhost and development environments
    return (strpos($site_url, 'localhost') !== false ||
            strpos($site_url, '127.0.0.1') !== false ||
            strpos($site_url, '.local') !== false ||
            strpos($site_url, '.dev') !== false ||
            strpos($site_url, '.test') !== false);
}

/**
 * Perform the actual database cleanup
 */
function fc_perform_database_cleanup() {
    global $wpdb;

    // Safety check
    if (!fc_is_cleanup_allowed()) {
        return array(
            'success' => false,
            'message' => 'Database cleanup is disabled on this environment for safety. This feature is only available on development/localhost environments.'
        );
    }

    $results = array();

    // Start transaction
    $wpdb->query('START TRANSACTION');

    try {
        // 1. Delete all products and their metadata
        $product_ids = $wpdb->get_col("
            SELECT ID FROM {$wpdb->posts}
            WHERE post_type IN ('product', 'product_variation')
        ");

        foreach ($product_ids as $product_id) {
            wp_delete_post($product_id, true);
        }
        $results['products_deleted'] = count($product_ids);

        // 2. Delete all orders and their metadata (both legacy posts and HPOS tables)
        $order_ids = $wpdb->get_col("
            SELECT ID FROM {$wpdb->posts}
            WHERE post_type IN ('shop_order', 'shop_order_refund')
        ");

        foreach ($order_ids as $order_id) {
            wp_delete_post($order_id, true);
        }
        $results['orders_deleted'] = count($order_ids);

        // 2b. Clean up WooCommerce order tables (HPOS)
        $hpos_orders_deleted = 0;
        if ($wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}wc_orders'") == $wpdb->prefix . 'wc_orders') {
            $hpos_orders_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}wc_orders");
            $wpdb->query("DELETE FROM {$wpdb->prefix}wc_order_addresses");
            $wpdb->query("DELETE FROM {$wpdb->prefix}wc_order_operational_data");
            $wpdb->query("DELETE FROM {$wpdb->prefix}wc_orders_meta");
        }

        // 2c. Clean up order items and item meta
        $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_order_items");
        $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_order_itemmeta");

        $results['orders_deleted'] += $hpos_orders_deleted;

        // 3. Delete competition tickets
        $tickets_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}competition_tickets");
        $results['tickets_deleted'] = $tickets_deleted;

        // 4. Delete instant wins (both tables)
        $instant_wins_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}instant_wins");
        $competition_instant_wins_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}competition_instant_wins");
        $results['instant_wins_deleted'] = $instant_wins_deleted + $competition_instant_wins_deleted;

        // 5. Delete competition winners (both table and posts)
        $winners_table_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}competition_winners");

        // Delete winner posts and their metadata
        $winner_post_ids = $wpdb->get_col("
            SELECT ID FROM {$wpdb->posts}
            WHERE post_type = 'winner'
        ");

        $winner_posts_deleted = 0;
        foreach ($winner_post_ids as $post_id) {
            wp_delete_post($post_id, true);
            $winner_posts_deleted++;
        }

        // Clear winner-related post meta from competition products
        $winner_meta_deleted = $wpdb->query("
            DELETE FROM {$wpdb->postmeta}
            WHERE meta_key IN (
                'winner_user_id',
                'winner_username',
                'winner_ticket_id',
                'winner_ticket_number',
                'status'
            )
        ");

        // Reset competition status to default for any remaining competitions
        $wpdb->query("
            UPDATE {$wpdb->postmeta}
            SET meta_value = 'active'
            WHERE meta_key = 'status'
            AND meta_value IN ('completed', 'winner_selected', 'awaiting_draw')
        ");

        $results['winners_deleted'] = $winners_table_deleted + $winner_posts_deleted;
        $results['winner_meta_deleted'] = $winner_meta_deleted;

        // 6. Clear wallet data from all users
        $wallet_meta_deleted = $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key = 'wallet_balance'");
        $results['wallet_meta_deleted'] = $wallet_meta_deleted;

        // 7. Delete wallet transactions table data
        $wallet_transactions_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}wallet_transactions");
        $results['wallet_transactions_deleted'] = $wallet_transactions_deleted;

        // 7b. Clean up additional WooCommerce order-related data
        $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_sessions");
        $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_downloadable_product_permissions");

        // Clear any remaining order-related transients and options
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_order_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_wc_order_%'");

        // 8. Delete customer users (preserve admin accounts)
        $customer_user_ids = $wpdb->get_col("
            SELECT u.ID
            FROM {$wpdb->users} u
            INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id
            WHERE um.meta_key = '{$wpdb->prefix}capabilities'
            AND um.meta_value NOT LIKE '%administrator%'
            AND um.meta_value NOT LIKE '%editor%'
            AND um.meta_value NOT LIKE '%shop_manager%'
        ");

        foreach ($customer_user_ids as $user_id) {
            wp_delete_user($user_id);
        }
        $results['users_deleted'] = count($customer_user_ids);

        // 10. Clean up orphaned metadata
        $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE post_id NOT IN (SELECT ID FROM {$wpdb->posts})");
        $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE user_id NOT IN (SELECT ID FROM {$wpdb->users})");

        // 11. Delete product images and attachments
        $attachment_ids = $wpdb->get_col("
            SELECT ID FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_parent IN (
                SELECT ID FROM {$wpdb->posts}
                WHERE post_type IN ('product', 'product_variation')
            )
        ");

        foreach ($attachment_ids as $attachment_id) {
            wp_delete_attachment($attachment_id, true);
        }
        $results['attachments_deleted'] = count($attachment_ids);

        // 12. Reset WooCommerce counters
        delete_option('woocommerce_order_count');
        delete_transient('wc_count_comments');

        // 13. Clear any cached data
        wp_cache_flush();

        // Commit transaction
        $wpdb->query('COMMIT');

        $results['success'] = true;
        $results['message'] = sprintf(
            'All test data has been successfully deleted. Summary: %d products, %d orders (including order items), %d tickets, %d instant wins, %d winners, %d winner meta fields, %d users, %d wallet balances cleared, %d wallet transactions deleted.',
            $results['products_deleted'],
            $results['orders_deleted'],
            $results['tickets_deleted'],
            $results['instant_wins_deleted'],
            $results['winners_deleted'],
            $results['winner_meta_deleted'],
            $results['users_deleted'],
            $results['wallet_meta_deleted'],
            $results['wallet_transactions_deleted']
        );

    } catch (Exception $e) {
        // Rollback on error
        $wpdb->query('ROLLBACK');
        $results['success'] = false;
        $results['message'] = 'Error during cleanup: ' . $e->getMessage();
    }

    return $results;
}

/**
 * Add admin notice for cleanup tool
 */
function fc_cleanup_admin_notice() {
    $screen = get_current_screen();
    if ($screen->id === 'tools_page_fc-database-cleanup') {
        return; // Don't show on the cleanup page itself
    }

    // Only show to administrators
    if (!current_user_can('manage_options')) {
        return;
    }

    // Check if we have test data
    $counts = fc_get_data_counts();
    $has_test_data = ($counts['products'] > 0 || $counts['orders'] > 0 || $counts['tickets'] > 0);

    if ($has_test_data) {
        ?>
        <div class="notice notice-info is-dismissible">
            <p>
                <strong>🧹 Database Cleanup Available:</strong>
                You have test data in your database.
                <a href="<?php echo admin_url('tools.php?page=fc-database-cleanup'); ?>">
                    Click here to clean up test data
                </a>
                before launching your website.
            </p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'fc_cleanup_admin_notice');

/**
 * Add cleanup button to admin bar
 */
function fc_add_cleanup_admin_bar($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }

    // Check if we have test data
    $counts = fc_get_data_counts();
    $has_test_data = ($counts['products'] > 0 || $counts['orders'] > 0 || $counts['tickets'] > 0);

    if ($has_test_data) {
        $wp_admin_bar->add_node(array(
            'id'    => 'fc-cleanup',
            'title' => '🧹 Cleanup Test Data',
            'href'  => admin_url('tools.php?page=fc-database-cleanup'),
            'meta'  => array(
                'title' => 'Clean up test data before launch'
            )
        ));
    }
}
add_action('admin_bar_menu', 'fc_add_cleanup_admin_bar', 100);

/**
 * Add dashboard widget for cleanup
 */
function fc_add_cleanup_dashboard_widget() {
    if (!current_user_can('manage_options')) {
        return;
    }

    wp_add_dashboard_widget(
        'fc_cleanup_widget',
        '🧹 Database Cleanup',
        'fc_cleanup_dashboard_widget_content'
    );
}
add_action('wp_dashboard_setup', 'fc_add_cleanup_dashboard_widget');

/**
 * Dashboard widget content
 */
function fc_cleanup_dashboard_widget_content() {
    $counts = fc_get_data_counts();
    $total_items = $counts['products'] + $counts['orders'] + $counts['tickets'] + $counts['instant_wins'] + $counts['winners'];

    if ($total_items > 0) {
        ?>
        <div style="text-align: center; padding: 20px;">
            <h3 style="color: #d63384; margin-top: 0;">⚠️ Test Data Detected</h3>
            <p>Your database contains <strong><?php echo $total_items; ?></strong> test items that should be cleaned up before launch.</p>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>Test Data Summary:</strong><br>
                <?php echo $counts['products']; ?> Products |
                <?php echo $counts['orders']; ?> Orders |
                <?php echo $counts['tickets']; ?> Tickets |
                <?php echo $counts['instant_wins']; ?> Instant Wins |
                <?php echo $counts['winners']; ?> Winners
            </div>

            <a href="<?php echo admin_url('tools.php?page=fc-database-cleanup'); ?>"
               class="button button-primary button-large"
               style="background: #d63384; border-color: #d63384;">
                🗑️ Clean Up Test Data
            </a>
        </div>
        <?php
    } else {
        ?>
        <div style="text-align: center; padding: 20px;">
            <h3 style="color: #198754; margin-top: 0;">✅ Database Clean</h3>
            <p>No test data found. Your database is ready for launch!</p>
        </div>
        <?php
    }
}

/**
 * Clean up wallet balance inconsistencies
 */
function fc_cleanup_wallet_inconsistencies() {
    global $wpdb;

    $results = array(
        'success' => false,
        'message' => '',
        'duplicates_cleaned' => 0,
        'users_affected' => 0,
        'cache_cleared' => 0,
        'transactions_cleared' => 0,
        'wallet_table_cleared' => 0
    );

    try {
        // Start transaction
        $wpdb->query('START TRANSACTION');

        // 1. Find all users with duplicate wallet_balance entries
        $users_with_duplicates = $wpdb->get_results(
            "SELECT user_id, COUNT(*) as count
             FROM {$wpdb->usermeta}
             WHERE meta_key = 'wallet_balance'
             GROUP BY user_id
             HAVING count > 1"
        );

        $duplicates_cleaned = 0;
        $users_affected = count($users_with_duplicates);

        // 2. Clean up duplicates for each user
        foreach ($users_with_duplicates as $user_data) {
            $user_id = $user_data->user_id;

            // Get all wallet_balance entries for this user
            $all_balances = $wpdb->get_results($wpdb->prepare(
                "SELECT umeta_id, meta_value FROM {$wpdb->usermeta}
                 WHERE user_id = %d AND meta_key = 'wallet_balance'
                 ORDER BY umeta_id DESC",
                $user_id
            ));

            if (count($all_balances) > 1) {
                // Keep the most recent entry (first in DESC order)
                $keep_entry = array_shift($all_balances);

                // Delete the rest
                foreach ($all_balances as $entry) {
                    $wpdb->delete(
                        $wpdb->usermeta,
                        array('umeta_id' => $entry->umeta_id),
                        array('%d')
                    );
                    $duplicates_cleaned++;
                }

                // Clear cache for this user
                wp_cache_delete($user_id, 'user_meta');
            }
        }

        // 3. Clear wallet transaction history (both user meta and database table)
        $transactions_cleared = 0;
        $wallet_table_cleared = 0;

        // Clear wallet_transactions user meta
        $transaction_meta_deleted = $wpdb->query(
            "DELETE FROM {$wpdb->usermeta} WHERE meta_key = 'wallet_transactions'"
        );
        $transactions_cleared += $transaction_meta_deleted;

        // Clear wallet_transactions database table if it exists
        $wallet_table_name = $wpdb->prefix . 'wallet_transactions';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$wallet_table_name'");
        if ($table_exists) {
            $wallet_table_cleared = $wpdb->query("DELETE FROM $wallet_table_name");
        }

        // 4. Clear all user meta cache to ensure fresh data
        $all_users = $wpdb->get_col("SELECT DISTINCT user_id FROM {$wpdb->usermeta} WHERE meta_key = 'wallet_balance'");
        $cache_cleared = 0;
        foreach ($all_users as $user_id) {
            wp_cache_delete($user_id, 'user_meta');
            wp_cache_delete($user_id, 'users');
            // Clear specific wallet balance cache
            wp_cache_delete("wallet_balance_$user_id", 'user_meta');
            $cache_cleared++;
        }

        // 5. Clear WordPress object cache completely
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // 6. Clear any transients that might be caching wallet data
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wallet_%' OR option_name LIKE '_transient_timeout_wallet_%'");

        // 7. Clear WooCommerce sessions that might have cached wallet data
        if (class_exists('WooCommerce')) {
            $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_sessions");
        }

        // 8. Force clear any persistent object cache
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('user_meta');
            wp_cache_flush_group('users');
        }

        // Commit transaction
        $wpdb->query('COMMIT');

        $results['success'] = true;
        $results['duplicates_cleaned'] = $duplicates_cleaned;
        $results['users_affected'] = $users_affected;
        $results['cache_cleared'] = $cache_cleared;
        $results['transactions_cleared'] = $transactions_cleared;
        $results['wallet_table_cleared'] = $wallet_table_cleared;

        if ($duplicates_cleaned > 0 || $transactions_cleared > 0 || $wallet_table_cleared > 0) {
            $message_parts = array();

            if ($duplicates_cleaned > 0) {
                $message_parts[] = sprintf('cleaned up %d duplicate wallet entries for %d users', $duplicates_cleaned, $users_affected);
            }

            if ($transactions_cleared > 0) {
                $message_parts[] = sprintf('cleared %d wallet transaction records', $transactions_cleared);
            }

            if ($wallet_table_cleared > 0) {
                $message_parts[] = sprintf('cleared %d wallet transaction table entries', $wallet_table_cleared);
            }

            $message_parts[] = sprintf('cleared cache for %d users', $cache_cleared);

            $results['message'] = 'Successfully ' . implode(', ', $message_parts) . '. Wallet balances should now be consistent across all pages.';
        } else {
            $results['message'] = sprintf(
                'No duplicate wallet entries or transactions found. Cleared cache for %d users to ensure fresh data.',
                $cache_cleared
            );
        }

    } catch (Exception $e) {
        // Rollback on error
        $wpdb->query('ROLLBACK');
        $results['success'] = false;
        $results['message'] = 'Error during wallet cleanup: ' . $e->getMessage();
    }

    return $results;
}

/**
 * Clear all possible caches
 */
function fc_clear_all_caches() {
    global $wpdb;

    // Clear WordPress object cache
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }

    // Clear all user meta cache
    $all_users = get_users(array('fields' => 'ID'));
    foreach ($all_users as $user_id) {
        wp_cache_delete($user_id, 'user_meta');
        wp_cache_delete($user_id, 'users');
        wp_cache_delete("wallet_balance_$user_id", 'user_meta');
    }

    // Clear transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' OR option_name LIKE '_transient_timeout_%'");

    // Clear WooCommerce sessions
    if (class_exists('WooCommerce')) {
        $wpdb->query("DELETE FROM {$wpdb->prefix}woocommerce_sessions");
    }

    // Clear persistent object cache groups
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('user_meta');
        wp_cache_flush_group('users');
        wp_cache_flush_group('options');
    }

    // Clear any opcache if available
    if (function_exists('opcache_reset')) {
        opcache_reset();
    }
}

/**
 * Force wallet display refresh
 */
function fc_force_wallet_refresh() {
    global $wpdb;

    // Clear all caches first
    fc_clear_all_caches();

    // Force refresh WooCommerce cart fragments
    if (class_exists('WooCommerce')) {
        // Clear WooCommerce fragment cache
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_cart_hash_%'");

        // Clear browser sessionStorage by setting a flag
        setcookie('force_wallet_refresh', time(), time() + 60, '/');
    }

    // Add JavaScript to force refresh cart fragments on next page load
    add_action('admin_footer', function() {
        echo '<script>
            if (typeof jQuery !== "undefined") {
                jQuery(document).ready(function($) {
                    // Force trigger cart fragments refresh
                    if (typeof wc_cart_fragments_params !== "undefined") {
                        $(document.body).trigger("wc_fragment_refresh");
                    }

                    // Also try to refresh after a short delay
                    setTimeout(function() {
                        if (typeof wc_cart_fragments_params !== "undefined") {
                            $(document.body).trigger("wc_fragment_refresh");
                        }
                    }, 1000);
                });
            }
        </script>';
    });
}
