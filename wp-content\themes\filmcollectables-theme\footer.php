<!-- Footer -->
<footer class="site-footer" style="background: #050608 !important; color: #fff !important; padding: 3rem 0 1.5rem 0 !important; display: block !important; visibility: visible !important; opacity: 1 !important; position: relative !important; z-index: 1000 !important; margin: 0 !important; border-top: 1px solid #222 !important;">
    <div class="container" style="max-width: 1200px !important; margin: 0 auto !important; padding: 0 1rem !important; background: transparent !important;">
        <div class="footer-content" style="display: flex !important; flex-wrap: wrap !important; justify-content: space-between !important; gap: 2rem !important; align-items: flex-start !important; background: transparent !important; color: #fff !important;">
            <!-- Social & Mailing List -->
            <div style="flex: 1 1 220px; min-width: 220px; max-width: 300px;">
                <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 0.75rem; letter-spacing: 1px;">SIGN UP TO OUR MAILING LIST</h3>
                <form id="footer-newsletter-form" style="display: flex; gap: 0.5rem; margin-bottom: 1.5rem;">
                    <input type="email" id="footer-email" name="email" placeholder="Your email address" required style="padding: 0.5rem 1rem; border-radius: 4px; border: none; outline: none; font-size: 1rem; flex: 1; background: #18191a; color: #fff;">
                    <button type="submit" id="footer-subscribe-btn" style="background: #ff6b35; color: #fff; border: none; border-radius: 4px; padding: 0.5rem 1.2rem; font-weight: bold; cursor: pointer;">Subscribe</button>
                </form>
                <div id="footer-newsletter-message" style="display: none; margin-top: 0.5rem; font-size: 0.9rem;"></div>
            </div>
            <!-- Menu -->
            <div style="flex: 1 1 140px; min-width: 140px; max-width: 180px;">
                <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 0.75rem; letter-spacing: 1px;">MENU</h3>
                <ul style="list-style: none; padding: 0; margin: 0; line-height: 2;">
                    <li><a href="<?php echo esc_url(home_url('/competitions/')); ?>" style="color: #fff; text-decoration: none;">Competitions</a></li>
                    <li><a href="<?php echo esc_url(home_url('/my-account/')); ?>" style="color: #fff; text-decoration: none;">My Account</a></li>
                    <li><a href="<?php echo esc_url(home_url('/faq/')); ?>" style="color: #fff; text-decoration: none;">FAQ</a></li>
                    <li><a href="<?php echo esc_url(home_url('/contact/')); ?>" style="color: #fff; text-decoration: none;">Contact</a></li>
                </ul>
            </div>
            <!-- Company Info -->
            <div style="flex: 1 1 260px; min-width: 220px; max-width: 320px;">
                <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 0.75rem; letter-spacing: 1px;">FILM COLLECTABLES</h3>
                <div style="line-height: 2;">
                    <span style="color: #ffd23f;">Address:</span> Ground Floor, Gallery Building, 65-69 Dublin Rd, Belfast, BT2 7HG<br>
                    <span style="color: #ffd23f;">Company No:</span> NI730025<br>
                    <span style="color: #ffd23f;">Email:</span> <a href="mailto:<EMAIL>" style="color: #ffd23f; text-decoration: none;"><EMAIL></a>
                </div>
            </div>
            <!-- Legal Info -->
            <div style="flex: 1 1 180px; min-width: 180px; max-width: 220px; display: flex; flex-direction: column; align-items: flex-start;">
                <h3 style="font-size: 1.1rem; font-weight: bold; margin-bottom: 0.75rem; letter-spacing: 1px;">LEGAL INFORMATION</h3>
                <ul style="list-style: none; padding: 0; margin: 0; line-height: 2; margin-bottom: 1rem;">
                    <li><a href="<?php echo esc_url(home_url('/terms-and-conditions/')); ?>" style="color: #fff; text-decoration: none;">Terms & Conditions</a></li>
                    <li><a href="<?php echo esc_url(home_url('/privacy/')); ?>" style="color: #fff; text-decoration: none;">Privacy Policy</a></li>
                </ul>
                <!-- Payment Icons -->
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; align-items: center;">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png" alt="Visa" style="height: 28px; background: #fff; border-radius: 4px; padding: 2px;">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png" alt="Mastercard" style="height: 28px; background: #fff; border-radius: 4px; padding: 2px;">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg" alt="Apple Pay" style="height: 28px; background: #fff; border-radius: 4px; padding: 2px;">
                    <img src="https://cdn-icons-png.flaticon.com/512/6124/6124998.png" alt="Google Pay" style="height: 28px; background: #fff; border-radius: 4px; padding: 2px;">
                </div>
            </div>
        </div>
        <div class="footer-bottom" style="text-align: center; color: #fff; font-size: 0.95rem;">
            <p style="margin: 0; color: #fff;">&copy; <?php echo date('Y'); ?> Film Collectables Competitions Limited</p>
        </div>
    </div>
</footer>

<style>
/* Footer styling for all pages */
footer.site-footer {
    background: #050608 !important;
    color: #fff !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1000 !important;
    clear: both !important;
}

footer.site-footer * {
    color: #fff !important;
}

footer.site-footer a:hover {
    color: #ffd23f !important;
}

footer.site-footer button {
    background: #ff6b35 !important;
    color: #fff !important;
    border: none !important;
}

footer.site-footer input[type="email"] {
    background: #18191a !important;
    color: #fff !important;
    border: none !important;
}

/* Yellow text for address labels on all pages */
footer.site-footer span[style*="color: #ffd23f"],
.site-footer span[style*="color: #ffd23f"] {
    color: #ffd23f !important;
}

/* Footer bottom border styling */
footer.site-footer .footer-bottom,
.site-footer .footer-bottom {
    border-top: 1px solid #222 !important;
    padding-top: 1rem !important;
    margin-top: 2rem !important;
    color: #fff !important;
}

footer.site-footer .footer-bottom p,
.site-footer .footer-bottom p {
    color: #fff !important;
}

/* Responsive footer layout */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column !important;
        gap: 2rem !important;
    }

    .footer-content > div {
        flex: none !important;
        min-width: 100% !important;
        max-width: 100% !important;
    }

    /* Newsletter form responsive */
    #footer-newsletter-form {
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    #footer-email {
        width: 100% !important;
        box-sizing: border-box !important;
    }

    #footer-subscribe-btn {
        width: 100% !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
    }
}

@media (max-width: 480px) {
    .site-footer {
        padding: 2rem 0 1rem 0 !important;
    }

    .container {
        padding: 0 0.75rem !important;
    }

    .footer-content h3 {
        font-size: 1rem !important;
        margin-bottom: 0.5rem !important;
    }

    .footer-content {
        gap: 1.5rem !important;
    }
}
</style>

<script>
// Footer newsletter signup handler
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('footer-newsletter-form');
    const emailInput = document.getElementById('footer-email');
    const submitBtn = document.getElementById('footer-subscribe-btn');
    const messageDiv = document.getElementById('footer-newsletter-message');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = emailInput.value.trim();
            if (!email) {
                showMessage('Please enter your email address.', 'error');
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = 'Subscribing...';

            // Send AJAX request
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=footer_newsletter_signup&email=' + encodeURIComponent(email) + '&nonce=<?php echo wp_create_nonce('footer_newsletter'); ?>'
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showMessage('✅ Successfully subscribed! Thank you for joining our mailing list.', 'success');
                    emailInput.value = '';
                } else {
                    const errorMessage = (data.data && data.data.message) ? data.data.message : 'Subscription failed. Please try again.';
                    showMessage('❌ ' + errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Newsletter signup error:', error);
                showMessage('❌ Network error. Please try again.', 'error');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Subscribe';
            });
        });
    }

    function showMessage(message, type) {
        messageDiv.textContent = message;
        messageDiv.style.display = 'block';
        messageDiv.style.color = type === 'success' ? '#4ade80' : '#f87171';

        // Hide message after 5 seconds
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
});
</script>

<?php wp_footer(); ?>
</body>
</html>
