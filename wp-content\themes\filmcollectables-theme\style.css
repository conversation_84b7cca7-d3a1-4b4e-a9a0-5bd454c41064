/*
Theme Name: Film Collectables
Description: A custom theme for film collectables competition website with WooCommerce integration
Version: 1.0
Author: Film Collectables Team
*/

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,400;1,500;1,700;1,900&display=swap');

/* CSS Custom Properties - Film Collectables Color Scheme */
:root {
    /* Primary Colors from Logo */
    --fc-black: #050608;
    --fc-orange: #ff6b35;
    --fc-orange-gradient: linear-gradient(to bottom, #ff8c42, #ff6b35, #ff4500);
    --fc-yellow: #ffd23f;
    --fc-blue-electric: #00bfff;
    --fc-blue-deep: #1e3a8a;
    --fc-blue-space: #0f172a;

    /* Gradient Colors */
    --fc-gradient-primary: linear-gradient(135deg, var(--fc-orange) 0%, var(--fc-yellow) 100%);
    --fc-gradient-blue: linear-gradient(135deg, var(--fc-blue-electric) 0%, var(--fc-blue-deep) 100%);
    --fc-gradient-space: linear-gradient(135deg, var(--fc-black) 0%, var(--fc-blue-space) 100%);

    /* Text Colors */
    --fc-text-primary: #ffffff;
    --fc-text-secondary: #e2e8f0;
    --fc-text-muted: #94a3b8;
    --fc-text-dark: var(--fc-black);

    /* Background Colors */
    --fc-bg-primary: var(--fc-black);
    --fc-bg-secondary: var(--fc-blue-space);
    --fc-bg-card: rgba(255, 255, 255, 0.05);
    --fc-bg-card-hover: rgba(255, 255, 255, 0.1);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
}

/* Competition Card Styles - Consistent sizing */
.competition-card {
    display: flex;
    flex-direction: column;
    background: var(--fc-bg-card);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    width: 100%; /* Ensure card takes full width of its column */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative; /* For absolute positioned badges */
}

.competition-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
    background: var(--fc-bg-card-hover);
}

.competition-image, .competition-card img.competition-image {
    width: 100%;
    height: 220px; /* Fixed height for all images */
    object-fit: cover; /* Maintain aspect ratio while filling container */
    display: block;
}

.card-content, .competition-content {
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
    flex-grow: 1; /* Allow content to expand to fill card height */
}

.card-title, .competition-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    margin-top: 0.5rem;
}

.card-title a {
    color: var(--fc-text-primary);
    text-decoration: none;
}

.card-title a:hover {
    color: var(--fc-orange);
}

.card-price, .competition-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--fc-orange);
    margin-bottom: 0.75rem;
}

.card-description {
    color: var(--fc-text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    flex-grow: 1; /* Allow description to take up available space */
    min-height: 60px; /* Minimum height for description */
}

.progress-container, .competition-progress {
    margin-bottom: 1rem;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: var(--fc-text-muted);
    margin-bottom: 0.25rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid rgba(0, 191, 255, 0.2);
}

.progress-fill {
    height: 100%;
    background: var(--fc-gradient-primary, linear-gradient(135deg, #ff6b35 0%, #ffd23f 100%));
    /* Remove all transitions to prevent animation conflicts */
    min-width: 0;
}

/* Instant Win Badge */
.instant-win-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 10;
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(0,0,0,0.2), 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    50% {
        box-shadow: 0 2px 4px rgba(0,0,0,0.2), 0 0 0 4px rgba(16, 185, 129, 0.1);
    }
}

.competition-countdown, .countdown-expired {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--fc-text-secondary);
    font-weight: bold;
}

.card-actions {
    margin-top: auto; /* Push button to bottom of card */
}

.competition-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 cards per row (each card is 4 columns out of 12) */
    gap: 1.5rem;
    margin-top: 2rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .competition-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 cards per row on medium screens */
    }
}

@media (max-width: 576px) {
    .competition-grid {
        grid-template-columns: 1fr; /* 1 card per row on small screens */
    }
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    line-height: 1.6;
    color: var(--fc-text-primary);
    background: var(--fc-bg-primary);
}

/* Ensure all text elements use Roboto */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button, input, textarea, select,
.btn, .filter-btn, .nav-menu a {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Basic layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Form Elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="date"],
input[type="number"],
textarea,
select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(0, 191, 255, 0.3);
    border-radius: 8px;
    color: var(--fc-text-primary);
    padding: 0.75rem;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--fc-blue-electric);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

input::placeholder,
textarea::placeholder {
    color: var(--fc-text-muted);
}

/* Add subtle space background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 211, 63, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Header */
.site-header {
    background: var(--fc-black);
    box-shadow: 0 2px 8px rgba(0, 191, 255, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    border-bottom: 1px solid rgba(0, 191, 255, 0.2);
}

.header-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    align-items: center;
    padding: 0.75rem 2rem;
    width: 100%;
    max-width: none;
    gap: 2rem;
}

.header-left {
    display: flex;
    justify-content: flex-start;
}

.header-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1.5rem;
}

/* Logo */
.logo {
    font-size: 2rem;
    font-weight: 700;
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    white-space: nowrap;
    display: flex;
    align-items: center;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.logo:hover {
    filter: brightness(1.1);
    text-shadow: 0 0 15px rgba(255, 107, 53, 0.8);
}

.logo img,
.custom-logo {
    max-height: 80px !important;
    width: auto !important;
    display: block;
}

.custom-logo-link {
    display: flex;
    align-items: center;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Cart Icon */
.cart-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--fc-gradient-blue);
    color: var(--fc-text-primary);
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid rgba(0, 191, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.cart-icon:hover {
    background: var(--fc-gradient-primary);
    border-color: var(--fc-orange);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.5);
    transform: translateY(-1px);
}

.cart-icon svg {
    width: 20px;
    height: 20px;
}

.cart-count {
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    position: absolute;
    top: -8px;
    right: -8px;
}

.cart-total {
    font-weight: bold;
    font-size: 0.875rem;
}

/* User Menu */
.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #374151;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.user-menu:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

/* Auth Links */
.auth-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.login-link, .register-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.login-link {
    color: #374151;
}

.login-link:hover {
    background: #f3f4f6;
    color: #4f46e5;
}

.register-link {
    background: #4f46e5;
    color: white;
    border: 1px solid #4f46e5;
}

.register-link:hover {
    background: white;
    color: #4f46e5;
    border: 1px solid #4f46e5;
}

.user-icon {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: inherit;
    text-decoration: none;
}

.user-icon svg {
    width: 18px;
    height: 18px;
}

/* Header Right Links */
.header-link {
    color: var(--fc-text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    position: relative;
}

.header-link:hover {
    color: var(--fc-yellow);
    text-decoration: none;
    text-shadow: 0 0 8px rgba(255, 211, 63, 0.6);
}

/* Legacy wallet link styling for compatibility */
.wallet-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--fc-text-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.wallet-link:hover {
    color: var(--fc-yellow);
    text-decoration: none;
    text-shadow: 0 0 8px rgba(255, 211, 63, 0.6);
}

.wallet-link svg {
    width: 18px;
    height: 18px;
}



/* Navigation */
.main-nav {
    display: flex;
    justify-content: center;
}

.main-nav ul,
.main-nav .nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
    justify-content: center;
}

.main-nav a {
    text-decoration: none;
    color: var(--fc-text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 0;
    transition: all 0.3s ease;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.main-nav a:hover {
    color: var(--fc-yellow);
    text-shadow: 0 0 8px rgba(255, 211, 63, 0.6);
}

.main-nav .current-menu-item a,
.main-nav .current_page_item a {
    color: var(--fc-yellow);
    font-weight: 600;
    position: relative;
}

.main-nav .current-menu-item a::after,
.main-nav .current_page_item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--fc-gradient-primary);
    border-radius: 1px;
}

/* Countdown Timer */
.competition-countdown {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    justify-content: space-between;
    margin: 1rem 0;
    width: 100%;
}

.countdown-unit {
    background: #000;
    color: white;
    padding: 1.25rem 1rem;
    border-radius: 6px;
    text-align: center;
    flex: 1;
    min-width: 0;
}

.countdown-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.countdown-label {
    font-size: 0.85rem;
    color: #fbbf24;
    text-transform: lowercase;
    font-weight: 500;
}

.countdown-expired {
    background: #dc2626;
    color: white;
    padding: 1.25rem 1.5rem;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    font-size: 1.1rem;
    width: 100%;
}

/* Competition card countdown adjustments */
.competition-card .competition-countdown {
    margin: 1rem 0;
    gap: 0.25rem;
}

.competition-card .countdown-unit {
    padding: 0.5rem 0.75rem;
    min-width: 45px;
}

.competition-card .countdown-number {
    font-size: 1.125rem;
}

.competition-card .countdown-label {
    font-size: 0.65rem;
}

/* Competition Info Bars */
.competition-info-bars {
    background: #f5f5f5;
    padding: 2rem 0 0 0;
}

.info-bars-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0;
    max-width: 1200px;
    margin: 0 auto;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.info-bar {
    background: #000;
    color: white;
    padding: 1rem;
    text-align: center;
    border-right: 1px solid #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.info-bar:last-child {
    border-right: none;
}

.info-icon {
    font-size: 1.1rem;
    opacity: 0.8;
}

.info-text {
    white-space: nowrap;
}

/* Guitar Gear Giveaway Style Competition Page */
.competition-detail {
    background: #f5f5f5;
}

/* Radio button styling for competition questions */
input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
    cursor: pointer !important;
}

/* Hover effects for radio labels */
label:has(input[type="radio"]):hover {
    background: #e9ecef !important;
    border-color: #fbbf24 !important;
}

/* Selected radio button styling */
label:has(input[type="radio"]:checked) {
    background: #fff3cd !important;
    border-color: #fbbf24 !important;
}

/* Ensure cursor pointer on all radio elements */
label:has(input[type="radio"]),
label:has(input[type="radio"]) *,
input[type="radio"] {
    cursor: pointer !important;
}

/* Button hover effects */
button[type="submit"]:hover {
    background: #333 !important;
}

/* Progress bar styling */
.progress-bar-guitar {
    background: linear-gradient(90deg, #fbbf24, #f59e0b);
}

/* Recent winners section styling */
.recent-winner-item:hover {
    background: #f1f3f4 !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Ticket Slider Styling */
#ticket-slider {
    width: 100% !important;
    background: linear-gradient(to right, #ccc 0%, #ccc 100%) !important;
    height: 6px !important;
    border-radius: 3px !important;
    outline: none !important;
    -webkit-appearance: none !important;
    cursor: pointer !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    margin: 0 !important;
    padding: 0 !important;
}

#ticket-slider::-webkit-slider-track {
    width: 100%;
    background: #ccc;
    height: 6px;
    border-radius: 3px;
    border: none;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

#ticket-slider::-webkit-slider-thumb {
    -webkit-appearance: none !important;
    appearance: none !important;
    width: 24px !important;
    height: 24px !important;
    background: #000 !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    border: 3px solid #fff !important;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2) !important;
    transition: all 0.2s ease !important;
}

#ticket-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 3px 8px rgba(0,0,0,0.3) !important;
}

#ticket-slider::-moz-range-track {
    width: 100%;
    background: #ccc;
    height: 6px;
    border-radius: 3px;
    border: none;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

#ticket-slider::-moz-range-thumb {
    width: 24px !important;
    height: 24px !important;
    background: #000 !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    border: 3px solid #fff !important;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2) !important;
    transition: all 0.2s ease !important;
}

#ticket-slider::-moz-range-thumb:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 3px 8px rgba(0,0,0,0.3) !important;
}

/* Ticket control buttons */
#decrease-tickets,
#increase-tickets {
    background: #6c757d !important;
    color: white !important;
    border: none !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 6px !important;
    font-size: 1.5rem !important;
    font-weight: bold !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    transition: all 0.2s ease !important;
}

#decrease-tickets:hover,
#increase-tickets:hover {
    background: #5a6268 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

#decrease-tickets:active,
#increase-tickets:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

/* Ticket display styling */
#ticket-display {
    background: #000 !important;
    color: white !important;
    font-weight: bold !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem !important;
    white-space: nowrap !important;
    transition: all 0.2s ease !important;
}

/* User Registration Form Styling */
.woocommerce-form-register .form-row {
    margin-bottom: 1rem;
}

.woocommerce-form-register .form-row label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.woocommerce-form-register .form-row input[type="text"],
.woocommerce-form-register .form-row input[type="email"],
.woocommerce-form-register .form-row input[type="password"],
.woocommerce-form-register .form-row input[type="date"],
.woocommerce-form-register .form-row input[type="tel"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.woocommerce-form-register .form-row input:focus {
    outline: none;
    border-color: #fbbf24;
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

.woocommerce-form-register .form-row .required {
    color: #dc3545;
}

.woocommerce-form-register .form-row .form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.woocommerce-form-register .privacy-notice {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    border-left: 4px solid #fbbf24;
    margin-top: 1rem;
}

.woocommerce-form-register .privacy-notice small {
    color: #495057;
    line-height: 1.4;
}



/* Modern My Account Page Styling - Card-Based Design */
/* Complete reset and override of WooCommerce account styles */
.woocommerce-account,
.woocommerce-account * {
    box-sizing: border-box;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Main account container */
.woocommerce-account {
    margin: 0;
    padding: 0;
    background: #f8fafc;
    min-height: 100vh;
    color: #1f2937;
}

/* Account page container */
.account-page-container {
    width: 100%;
    margin: 0 auto;
    padding: 2rem;
    box-sizing: border-box;
    overflow: visible;
}

/* Grid layout */
.account-grid {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: 3rem;
    align-items: start;
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

/* Sidebar styling */
.account-sidebar {
    position: static;
    align-self: flex-start;
    width: 320px;
    flex-shrink: 0;
}

/* Profile card */
.account-profile-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

/* Profile header */
.profile-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Profile avatar */
.profile-avatar {
    width: 48px;
    height: 48px;
    background: #0049a3;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-icon {
    width: 24px;
    height: 24px;
    color: #ffffff;
}

/* Profile info */
.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.profile-email {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    word-break: break-word;
}

/* Modern navigation */
.modern-account-navigation {
    padding: 0;
}

.nav-item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    border-right: 2px solid transparent;
    text-decoration: none;
}

.nav-item:hover {
    background: #f3f4f6;
    color: #1f2937;
    text-decoration: none;
}

.nav-item.active {
    background: rgba(0, 73, 163, 0.1);
    color: #0049a3;
    border-right-color: #0049a3;
    box-shadow: 0 0 10px rgba(0, 73, 163, 0.2);
}

.nav-item.logout-item {
    color: #dc2626;
}

.nav-item.logout-item:hover {
    background: #fef2f2;
    color: #dc2626;
    text-decoration: none;
}

.nav-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.nav-label {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.nav-separator {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

/* Main content area */
.account-main-content {
    min-height: 600px;
    width: 100%;
    min-width: 0;
    overflow: visible;
    position: relative;
}

.woocommerce-MyAccount-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    color: #1f2937;
    width: 100%;
    box-sizing: border-box;
    max-width: 100%;
}

/* Override WooCommerce default width and float */
.woocommerce-account .woocommerce-MyAccount-content {
    width: 100% !important;
    float: none !important;
}

/* Force all content to start at the very top */
.woocommerce-MyAccount-content > * {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Specifically target the account details wrapper */
.woocommerce-MyAccount-content .account-details-wrapper {
    margin-top: 0 !important;
    padding: 0 !important;
    position: relative;
    top: 0 !important;
    width: 100%;
}

/* Target the account details wrapper specifically */
.account-details-wrapper {
    margin: 0 !important;
    padding: 0 !important;
    max-width: 100%;
}

/* Aggressively remove any top spacing from all elements */
.woocommerce-MyAccount-content > *,
.woocommerce-MyAccount-content > * > *,
.woocommerce-MyAccount-content > * > * > * {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Specifically target any WooCommerce elements that might add space */
.woocommerce-MyAccount-content .woocommerce-notices-wrapper,
.woocommerce-MyAccount-content .woocommerce-message,
.woocommerce-MyAccount-content .woocommerce-error,
.woocommerce-MyAccount-content .woocommerce-info,
.woocommerce-MyAccount-content .woocommerce-form,
.woocommerce-MyAccount-content form {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Force the account details wrapper to start at the very top */
.woocommerce-MyAccount-content .account-details-wrapper:first-child {
    position: relative;
    top: 0 !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Content headers */
.woocommerce-MyAccount-content h2,
.woocommerce-MyAccount-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.woocommerce-MyAccount-content p.description {
    color: #6b7280;
    margin: 0 0 2rem 0;
    font-size: 0.875rem;
}

/* Form styling */
.woocommerce-account .woocommerce-EditAccountForm {
    max-width: 100%;
    width: 100%;
}

/* Form Row Styling - Film Collectables Design */
.woocommerce-account .woocommerce-form-row {
    margin-bottom: 2.5rem;
    position: relative;
    box-sizing: border-box;
}

/* Override width for specific form row types */
.woocommerce-account .woocommerce-form-row.form-row-wide {
    width: 100% !important;
    clear: both !important;
}

.woocommerce-account .woocommerce-form-row.form-row-first,
.woocommerce-account .woocommerce-form-row.form-row-last {
    width: auto !important;
    clear: none !important;
}

/* Force proper form layout with maximum specificity */
body.woocommerce-account .woocommerce .woocommerce-EditAccountForm .woocommerce-form-row.form-row-first,
body.woocommerce-account .woocommerce .woocommerce-EditAccountForm p.woocommerce-form-row.form-row-first {
    width: 48% !important;
    float: left !important;
    margin-bottom: 2.5rem !important;
    margin-right: 4% !important;
    clear: none !important;
    display: block !important;
}

body.woocommerce-account .woocommerce .woocommerce-EditAccountForm .woocommerce-form-row.form-row-last,
body.woocommerce-account .woocommerce .woocommerce-EditAccountForm p.woocommerce-form-row.form-row-last {
    width: 48% !important;
    float: right !important;
    margin-bottom: 2.5rem !important;
    margin-left: 0 !important;
    clear: none !important;
    display: block !important;
}

body.woocommerce-account .woocommerce .woocommerce-EditAccountForm .woocommerce-form-row.form-row-wide,
body.woocommerce-account .woocommerce .woocommerce-EditAccountForm p.woocommerce-form-row.form-row-wide {
    width: 100% !important;
    clear: both !important;
    margin-bottom: 2.5rem !important;
    float: none !important;
    display: block !important;
}

/* Clear floats after form rows */
body.woocommerce-account .woocommerce-EditAccountForm .clear,
body.woocommerce-account .woocommerce-EditAccountForm div.clear {
    clear: both !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    display: block !important;
}

/* Form labels */
.woocommerce-account .woocommerce-form-row label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-family: 'Roboto', sans-serif;
}

/* Form inputs */
.woocommerce-account .woocommerce-Input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    background: #ffffff;
    color: #1f2937;
    transition: all 0.2s ease;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

/* Account details wrapper */
.account-details-wrapper {
    width: 100%;
    max-width: none;
}

/* Edit account form specific styling */
.woocommerce-EditAccountForm {
    width: 100%;
    max-width: none;
    overflow: hidden; /* Contains floated elements */
    margin: 0 !important;
    padding-top: 0 !important;
}

/* Remove any spacing from WooCommerce notices or actions */
.woocommerce-notices-wrapper {
    margin: 0 !important;
    padding: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Hide any empty notices or elements that might be taking up space */
.woocommerce-notices-wrapper:empty {
    display: none !important;
}

/* Ensure no extra spacing from before/after form actions */
.woocommerce-MyAccount-content .woocommerce-message,
.woocommerce-MyAccount-content .woocommerce-error,
.woocommerce-MyAccount-content .woocommerce-info {
    margin-top: 0 !important;
}

/* Target any potential invisible elements or hooks */
.woocommerce-MyAccount-content > div:empty,
.woocommerce-MyAccount-content > p:empty,
.woocommerce-MyAccount-content > span:empty {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.woocommerce-EditAccountForm h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 2rem 0 !important;
    padding: 0 0 0.75rem 0 !important;
    border-bottom: 2px solid #d4a574;
}

/* Force remove any spacing from elements that might be above the form */
.woocommerce-MyAccount-content > .account-details-wrapper:first-child .woocommerce-EditAccountForm h3:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Ensure form container can handle floated elements */
.woocommerce-EditAccountForm::after {
    content: "";
    display: table;
    clear: both;
}

/* Input focus and hover states */
.woocommerce-account .woocommerce-Input:focus {
    outline: none;
    border-color: #0049a3;
    box-shadow: 0 0 0 3px rgba(0, 73, 163, 0.1);
}

.woocommerce-account .woocommerce-Input:hover {
    border-color: #9ca3af;
}

/* Input placeholder styling */
.woocommerce-account .woocommerce-Input::placeholder {
    color: #9ca3af;
}

/* Special styling for different input types with Film Collectables icons */
.woocommerce-account input[type="email"] {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%********" viewBox="0 0 16 16"><path d="M2 2a2 2 0 0 0-2 2v8.01A2 2 0 0 0 2 14h5.5a.5.5 0 0 0 0-1H2a1 1 0 0 1-.966-.741l5.64-3.471L8 9.583l1.326-.795 5.64 3.47A1 1 0 0 1 14 13h-2.5a.5.5 0 0 0 0 1H14a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2Zm3.708 6.208L1 11.105V5.383l4.708 2.825ZM1 4.217V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v.217l-7 4.2-7-4.2Z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 1.5rem center;
    padding-right: 3.5rem;
}

.woocommerce-account input[type="tel"] {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%********" viewBox="0 0 16 16"><path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122L9.98 10.97a.678.678 0 0 1-.358-.063l-5.476-2.99a.678.678 0 0 1-.063-.358l.548-1.805a.678.678 0 0 0-.122-.58L3.654 1.328z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 1.5rem center;
    padding-right: 3.5rem;
}

.woocommerce-account input[type="date"] {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%********" viewBox="0 0 16 16"><path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 1.5rem center;
    padding-right: 3.5rem;
}

/* Password fields styling */
.woocommerce-account .woocommerce-form-row--password {
    position: relative;
}

.woocommerce-account input[type="password"] {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%********" viewBox="0 0 16 16"><path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 1.5rem center;
    padding-right: 3.5rem;
}

/* Remove conflicting grid layout - using float layout instead */

/* Fix any spacing issues in form rows */
.woocommerce-account .woocommerce-form-row input,
.woocommerce-account .woocommerce-form-row select,
.woocommerce-account .woocommerce-form-row textarea {
    box-sizing: border-box !important;
    margin: 0 !important;
    width: 100% !important;
}

/* Force proper alignment for all form elements */
.woocommerce-account .woocommerce-Input,
.woocommerce-account .woocommerce-form-row .woocommerce-Input {
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
}

/* Button styling */
.woocommerce-account .woocommerce-Button {
    background: #0049a3;
    color: #ffffff;
    border: 2px solid #0049a3;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    display: inline-block;
    font-family: 'Roboto', sans-serif;
    box-shadow: 0 0 10px rgba(0, 73, 163, 0.3);
}

.woocommerce-account .woocommerce-Button:hover {
    background: #003d82;
    border-color: #003d82;
    box-shadow: 0 0 20px rgba(0, 73, 163, 0.5);
    transform: translateY(-1px);
}

.woocommerce-account .woocommerce-Button:active {
    transform: translateY(0);
}

/* Modern card design */
.modern-account-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.content-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
}

.content-header .description {
    color: #6b7280;
    margin: 0 0 2rem 0;
    font-size: 0.875rem;
}

.account-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.card-content {
    padding: 1.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.field-description {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0.25rem 0 0 0;
    line-height: 1.4;
}

/* Prevent content overflow and clipping */
.modern-account-content {
    width: 100%;
    max-width: 100%;
    overflow: visible;
    box-sizing: border-box;
}

.modern-account-content * {
    box-sizing: border-box;
}

/* Ensure tables don't overflow */
.account-card {
    width: 100%;
    max-width: 100%;
    overflow: visible;
}

.account-card .card-content {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

/* Form Description Text - Film Collectables Style */
.woocommerce-account .woocommerce-form-row p,
.woocommerce-account .woocommerce-form-row span em {
    color: #666666;
    font-size: 0.875rem;
    margin-top: 0.75rem;
    line-height: 1.5;
    font-family: 'Roboto', sans-serif;
}

/* Required Field Indicators */
.woocommerce-account .required {
    color: var(--fc-orange);
    font-weight: bold;
}

/* Form Validation Styling - Film Collectables Style */
.woocommerce-account .woocommerce-Input.woocommerce-invalid {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
}

.woocommerce-account .woocommerce-Input.woocommerce-validated {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
}

/* Password Section Styling - Film Collectables Style */
.woocommerce-account .password-section {
    border: 2px solid #0049a3;
    border-radius: 12px;
    padding: 2rem;
    margin: 3rem 0;
    background: #f8f9fa;
    box-shadow:
        0 0 20px rgba(0, 73, 163, 0.2),
        0 4px 15px rgba(0, 0, 0, 0.1);
}

.woocommerce-account .password-section legend {
    background: var(--fc-orange-gradient);
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--fc-orange);
    border-radius: 8px;
    font-weight: 700;
    color: #ffffff;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Roboto', sans-serif;
    box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
}

/* Form Submit Wrapper - Film Collectables Style */
.woocommerce-account .form-submit-wrapper {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2.5rem;
    border-top: 2px solid #e9ecef;
    position: relative;
}

.woocommerce-account .form-submit-wrapper::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: var(--fc-orange-gradient);
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

/* Account details wrapper - centered within container */
.account-details-wrapper {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
}

/* Clear fix for form rows */
.woocommerce-account .clear {
    clear: both;
    height: 0;
    line-height: 0;
}

/* Loading state for save button */
.woocommerce-account .woocommerce-Button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Success Message Styling - Film Collectables Style */
.woocommerce-account .woocommerce-message {
    background: rgba(40, 167, 69, 0.1);
    border: 2px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
    padding: 1.25rem 1.75rem;
    border-radius: 12px;
    margin-bottom: 2.5rem;
    font-weight: 600;
    font-family: 'Roboto', sans-serif;
    box-shadow:
        0 0 20px rgba(40, 167, 69, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    position: relative;
}

.woocommerce-account .woocommerce-message::before {
    content: '✓';
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    font-weight: bold;
}

.woocommerce-account .woocommerce-message {
    padding-left: 3rem;
}

/* Error Message Styling - Film Collectables Style */
.woocommerce-account .woocommerce-error {
    background: rgba(220, 53, 69, 0.1);
    border: 2px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
    padding: 1.25rem 1.75rem;
    border-radius: 12px;
    margin-bottom: 2.5rem;
    font-weight: 600;
    font-family: 'Roboto', sans-serif;
    box-shadow:
        0 0 20px rgba(220, 53, 69, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    position: relative;
}

.woocommerce-account .woocommerce-error::before {
    content: '⚠';
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    font-weight: bold;
}

.woocommerce-account .woocommerce-error {
    padding-left: 3rem;
}

/* Responsive Design for Modern Account Page */
@media (max-width: 1024px) {
    .account-grid {
        grid-template-columns: 280px 1fr;
        gap: 2rem;
        max-width: 100%;
    }

    .account-page-container {
        padding: 1.5rem;
        max-width: 100%;
    }

    .account-sidebar {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .account-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .account-page-container {
        padding: 1rem;
    }

    .account-sidebar {
        position: static;
        width: 100%;
    }

    .profile-header {
        padding: 1rem;
    }

    .nav-item {
        padding: 1rem 1.5rem;
    }

    .woocommerce-MyAccount-content {
        padding: 1.5rem;
    }

    .card-header,
    .card-content {
        padding: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Adjust form layout for mobile */
    body.woocommerce-account .woocommerce .woocommerce-EditAccountForm .woocommerce-form-row.form-row-first,
    body.woocommerce-account .woocommerce .woocommerce-EditAccountForm p.woocommerce-form-row.form-row-first,
    body.woocommerce-account .woocommerce .woocommerce-EditAccountForm .woocommerce-form-row.form-row-last,
    body.woocommerce-account .woocommerce .woocommerce-EditAccountForm p.woocommerce-form-row.form-row-last {
        width: 100% !important;
        float: none !important;
        margin-right: 0 !important;
    }
}

@media (max-width: 480px) {
    .account-page-container {
        padding: 0.75rem;
    }

    .profile-header {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .nav-item {
        padding: 0.875rem 1rem;
        font-size: 0.8rem;
    }

    .woocommerce-MyAccount-content {
        padding: 1rem;
    }

    .woocommerce-account .woocommerce-Button {
        width: 100%;
    }

    .dashboard-tabs {
        margin-bottom: 1.5rem;
    }

    .tab-button {
        padding: 1rem 1.5rem !important;
        font-size: 0.8rem !important;
        min-height: 50px !important;
    }

    .dashboard-tabs {
        width: 100% !important;
    }
}

/* Dashboard Tabs - Fixed Layout and Centering */
.dashboard-tabs {
    display: flex !important;
    width: 100% !important;
    margin: 1rem 0 !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    background: #ffffff !important;
    clear: both !important;
    position: relative !important;
}

.tab-button {
    background: #f9fafb !important;
    border: none !important;
    border-right: 1px solid #e5e7eb !important;
    padding: 1rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    color: #6b7280 !important;
    font-family: 'Roboto', sans-serif !important;
    text-align: center !important;
    flex: 1 !important;
    width: 50% !important;
    height: 50px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    position: relative !important;
    margin: 0 !important;
    float: none !important;
}

.tab-button:last-child {
    border-right: none !important;
}

.tab-button:hover {
    background: #ffffff !important;
    color: #1f2937 !important;
}

.tab-button.active {
    background: #0049a3 !important;
    color: #ffffff !important;
    box-shadow:
        0 0 15px rgba(0, 73, 163, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Force proper tab styling - override any conflicting styles */
.modern-account-content .dashboard-tabs {
    width: 100% !important;
    max-width: none !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.modern-account-content .tab-button {
    float: none !important;
    margin: 0 !important;
    border-radius: 0 !important;
}

.modern-account-content .tab-button:first-child {
    border-top-left-radius: 7px !important;
    border-bottom-left-radius: 7px !important;
}

.modern-account-content .tab-button:last-child {
    border-top-right-radius: 7px !important;
    border-bottom-right-radius: 7px !important;
}

/* Empty State Cards */
.empty-state-card {
    background: #ffffff;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    margin: 2rem 0;
}

.empty-state-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    color: #9ca3af;
    margin: 0 auto 1.5rem auto;
    display: block;
}

.empty-state-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.75rem 0;
}

.empty-state-card p {
    color: #6b7280;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

.empty-state-card .woocommerce-Button {
    margin-top: 0;
}

/* Modern tickets grid */
.tickets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1rem;
}

.ticket-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.ticket-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.ticket-header h3 {
    margin: 0;
    font-size: 1.125rem;
    color: #1f2937;
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
}

.ticket-number {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'Roboto', sans-serif;
}

.ticket-details p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    color: #6b7280;
    font-family: 'Roboto', sans-serif;
}

/* Badge styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.used {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

/* WooCommerce Tables Styling */
.woocommerce-account .woocommerce-orders-table,
.woocommerce-account .shop_table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
}

.woocommerce-account .woocommerce-orders-table th,
.woocommerce-account .shop_table th {
    background: #f9fafb;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.woocommerce-account .woocommerce-orders-table td,
.woocommerce-account .shop_table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    color: #1f2937;
    font-size: 0.875rem;
}

.woocommerce-account .woocommerce-orders-table tr:last-child td,
.woocommerce-account .shop_table tr:last-child td {
    border-bottom: none;
}

.woocommerce-account .woocommerce-orders-table tr:hover,
.woocommerce-account .shop_table tr:hover {
    background: #f9fafb;
}

/* Order status badges */
.woocommerce-account .woocommerce-orders-table .order-status,
.woocommerce-account .shop_table .order-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.woocommerce-account .order-status.status-completed,
.woocommerce-account .order-status.completed {
    background: #dcfce7;
    color: #166534;
}

.woocommerce-account .order-status.status-processing,
.woocommerce-account .order-status.processing {
    background: #fef3c7;
    color: #92400e;
}

.woocommerce-account .order-status.status-pending,
.woocommerce-account .order-status.pending {
    background: #f3f4f6;
    color: #6b7280;
}

.woocommerce-account .order-status.status-on-hold {
    background: #fef3c7;
    color: #92400e;
}

.woocommerce-account .order-status.status-cancelled {
    background: #fef2f2;
    color: #dc2626;
}

.woocommerce-account .order-status.status-refunded {
    background: #f3f4f6;
    color: #6b7280;
}

.woocommerce-account .order-status.status-failed {
    background: #fef2f2;
    color: #dc2626;
}

/* Order table action buttons */
.woocommerce-account .woocommerce-orders-table .button,
.woocommerce-account .shop_table .button {
    background: #0049a3 !important;
    color: #ffffff !important;
    border: 1px solid #0049a3 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    display: inline-block !important;
    transition: all 0.2s ease !important;
    margin: 0.25rem !important;
}

.woocommerce-account .woocommerce-orders-table .button:hover,
.woocommerce-account .shop_table .button:hover {
    background: #003d82 !important;
    border-color: #003d82 !important;
    transform: translateY(-1px) !important;
}

/* Responsive table styling */
@media (max-width: 768px) {
    .woocommerce-account .woocommerce-orders-table,
    .woocommerce-account .shop_table {
        font-size: 0.875rem;
    }

    .woocommerce-account .woocommerce-orders-table th,
    .woocommerce-account .shop_table th {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    .woocommerce-account .woocommerce-orders-table td,
    .woocommerce-account .shop_table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    .woocommerce-account .woocommerce-orders-table .button,
    .woocommerce-account .shop_table .button {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.7rem !important;
    }
}

/* Status Badges - Film Collectables Style */
.status-active {
    color: #28a745;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
}

.status-drawn {
    color: #666666;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
}

.status-winner {
    color: var(--fc-orange);
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
}

/* Legacy no-tickets styling - now using empty-state-card */
.no-tickets {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
    background: #ffffff;
    border-radius: 12px;
    border: 2px dashed #d1d5db;
}

.no-tickets p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    font-family: 'Roboto', sans-serif;
    color: #1f2937;
}

/* Wins Page - Film Collectables Style */
.account-wins h2 {
    margin-bottom: 2.5rem;
    color: #333333;
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
    font-size: 2rem;
}

.wins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
}

.win-card {
    background: var(--fc-orange-gradient);
    border-radius: 12px;
    padding: 2rem;
    color: #ffffff;
    box-shadow:
        0 0 25px rgba(255, 107, 53, 0.4),
        0 8px 20px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.win-card:hover {
    transform: translateY(-3px);
    box-shadow:
        0 0 35px rgba(255, 107, 53, 0.6),
        0 12px 30px rgba(0, 0, 0, 0.3);
}

.win-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.win-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    color: #ffffff;
}

.win-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.win-details p {
    margin: 0.75rem 0;
    font-size: 0.9rem;
    font-family: 'Roboto', sans-serif;
    color: #ffffff;
    opacity: 0.9;
}

.no-wins {
    text-align: center;
    padding: 4rem 2rem;
    color: #666666;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #0049a3;
}

.no-wins p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    font-family: 'Roboto', sans-serif;
    color: #333333;
}

.no-wins-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.no-wins h3 {
    margin-bottom: 1rem;
    color: #495057;
}

/* Responsible Gaming Page */
.account-responsible-gaming h2 {
    margin-bottom: 2rem;
    color: #000;
}

.rg-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #d4a574;
}

.rg-section h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #000;
}

.rg-section ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.rg-section li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.activity-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-weight: 600;
    color: #495057;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: bold;
    color: #000;
}

.help-resources {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.help-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.help-item strong {
    color: #000;
}

.help-item a {
    color: #d4a574;
    text-decoration: none;
}

.help-item a:hover {
    text-decoration: underline;
}

.account-controls {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.button-secondary {
    background: #6c757d;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    transition: background 0.2s ease;
}

.button-secondary:hover {
    background: #5a6268;
    color: white;
}

/* Responsive Design - Tablet */
@media (max-width: 1024px) and (min-width: 769px) {
    .woocommerce-account .woocommerce {
        max-width: 1200px;
        padding: 0 1.5rem;
    }

    .woocommerce-account .woocommerce-MyAccount-navigation {
        width: 280px !important;
        margin-right: 2.5rem !important;
    }

    .woocommerce-account .woocommerce-MyAccount-content {
        margin-left: 310px !important;
    }
}

/* Responsive Design - Mobile */
@media (max-width: 768px) {
    .woocommerce-account .woocommerce {
        padding: 0 1rem;
        margin: 2rem auto;
    }

    .woocommerce-account .woocommerce-MyAccount-navigation {
        width: 100% !important;
        float: none !important;
        margin-right: 0 !important;
        margin-bottom: 2rem !important;
    }

    .woocommerce-account .woocommerce-MyAccount-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 2rem;
    }
}

    .tickets-grid,
    .wins-grid {
        grid-template-columns: 1fr;
    }

    .account-controls {
        flex-direction: column;
    }

    .activity-stats,
    .help-resources {
        grid-template-columns: 1fr;
    }

    /* Mobile form layout */
    .woocommerce-account .woocommerce-form-row--first,
    .woocommerce-account .woocommerce-form-row--last {
        width: 100%;
        display: block;
    }

    .woocommerce-account .woocommerce-form-row--last {
        margin-left: 0;
    }

    /* Stack form fields on mobile */
    .woocommerce-account .woocommerce-form-row--first,
    .woocommerce-account .woocommerce-form-row--last {
        width: 100% !important;
        display: block !important;
        margin-left: 0 !important;
        margin-bottom: 1rem;
    }
}

/* Responsive countdown */
@media (max-width: 768px) {
    .countdown-unit {
        padding: 0.5rem 0.75rem;
        min-width: 50px;
    }

    .countdown-number {
        font-size: 1.25rem;
    }

    .countdown-label {
        font-size: 0.7rem;
    }
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--fc-text-primary);
    margin: 2px 0;
    transition: all 0.3s ease;
    border-radius: 1px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation Overlay */
.mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Navigation Menu */
.mobile-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    background: var(--fc-bg-primary);
    z-index: 999;
    transition: right 0.3s ease;
    overflow-y: auto;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
}

.mobile-nav.active {
    right: 0;
}

.mobile-nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: var(--fc-bg-secondary);
}

.mobile-nav-header h3 {
    color: var(--fc-text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-nav-close {
    background: none;
    border: none;
    color: var(--fc-text-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.mobile-nav-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--fc-yellow);
}

.mobile-nav-content {
    padding: 1.5rem 0;
}

/* Mobile Navigation Menu Items */
.mobile-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-menu li {
    margin: 0;
}

.mobile-nav-menu a {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--fc-text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.mobile-nav-menu a:hover,
.mobile-nav-menu .current-menu-item a,
.mobile-nav-menu .current_page_item a {
    background: rgba(0, 73, 163, 0.1);
    color: var(--fc-yellow);
    border-left-color: var(--fc-yellow);
    text-shadow: 0 0 8px rgba(255, 211, 63, 0.6);
}

/* Mobile Navigation Actions */
.mobile-nav-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    color: var(--fc-text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.mobile-nav-link:hover {
    background: rgba(0, 73, 163, 0.1);
    color: var(--fc-yellow);
    border-left-color: var(--fc-yellow);
    text-decoration: none;
}

.mobile-nav-link svg {
    flex-shrink: 0;
}

/* Prevent body scroll when mobile menu is open */
body.mobile-nav-open {
    overflow: hidden;
}

/* Mobile menu animations and transitions */
@media (max-width: 768px) {
    .mobile-nav {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .mobile-nav-menu a {
        transform: translateX(20px);
        opacity: 0;
        animation: slideInFromRight 0.3s ease forwards;
    }

    .mobile-nav.active .mobile-nav-menu li:nth-child(1) a { animation-delay: 0.1s; }
    .mobile-nav.active .mobile-nav-menu li:nth-child(2) a { animation-delay: 0.15s; }
    .mobile-nav.active .mobile-nav-menu li:nth-child(3) a { animation-delay: 0.2s; }
    .mobile-nav.active .mobile-nav-menu li:nth-child(4) a { animation-delay: 0.25s; }
    .mobile-nav.active .mobile-nav-menu li:nth-child(5) a { animation-delay: 0.3s; }
    .mobile-nav.active .mobile-nav-menu li:nth-child(6) a { animation-delay: 0.35s; }

    .mobile-nav-link {
        transform: translateX(20px);
        opacity: 0;
        animation: slideInFromRight 0.3s ease forwards;
        animation-delay: 0.4s;
    }
}

@keyframes slideInFromRight {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Improved mobile menu styling for better UX */
@media (max-width: 480px) {
    .mobile-nav {
        width: 100%;
        max-width: 100vw;
    }

    .mobile-nav-header {
        padding: 1rem;
    }

    .mobile-nav-content {
        padding: 1rem 0;
    }

    .mobile-nav-menu a,
    .mobile-nav-link {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .header-content {
        grid-template-columns: 1fr auto;
        grid-template-rows: auto;
        padding: 1rem;
        gap: 1rem;
        align-items: center;
    }

    .header-left {
        justify-content: flex-start;
    }

    .desktop-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .header-right {
        justify-content: flex-end;
        gap: 1rem;
    }

    .header-right .header-link {
        display: none;
    }

    .logo {
        font-size: 1.5rem;
    }

    .logo img,
    .custom-logo {
        max-height: 70px !important;
    }

    .cart-icon {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    .cart-total {
        display: none;
    }
}

/* Hero Slider */
.hero-slider {
    position: relative;
    height: 600px;
    overflow: hidden;
    border-top: 6px solid var(--fc-blue-electric);
    border-bottom: 6px solid var(--fc-blue-electric);
    box-shadow:
        0 -8px 20px rgba(0, 191, 255, 0.4),
        0 8px 20px rgba(0, 191, 255, 0.4);
    animation: neonGlow 3s ease-in-out infinite alternate;
}

/* Neon glow animation for hero borders */
@keyframes neonGlow {
    0% {
        box-shadow:
            0 -8px 20px rgba(0, 191, 255, 0.4),
            0 8px 20px rgba(0, 191, 255, 0.4);
    }
    100% {
        box-shadow:
            0 -12px 30px rgba(0, 191, 255, 0.6),
            0 12px 30px rgba(0, 191, 255, 0.6);
    }
}

.slider-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    display: flex;
    align-items: center;
}

.hero-slide.active {
    opacity: 1;
}

.slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(
            135deg,
            var(--fc-black) 0%,
            rgba(5, 6, 8, 0.8) 25%,
            rgba(5, 6, 8, 0.4) 50%,
            rgba(5, 6, 8, 0.2) 75%,
            transparent 100%
        ),
        linear-gradient(
            to bottom,
            rgba(0, 191, 255, 0.1) 0%,
            transparent 20%,
            transparent 80%,
            rgba(255, 107, 53, 0.1) 100%
        );
}

.slide-content {
    position: relative;
    z-index: 2;
    width: 100%;
    color: white;
    text-align: center;
}

.slide-text {
    max-width: 600px;
    margin: 0 auto;
}

.slide-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.slide-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.slide-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-primary {
    background: #000000;
    color: #ffffff;
    border: 2px solid #0049a3;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    box-shadow:
        0 0 10px rgba(0, 73, 163, 0.5),
        0 0 20px rgba(0, 73, 163, 0.3),
        0 0 30px rgba(0, 73, 163, 0.1);
}

.btn-primary:hover {
    background: var(--fc-orange-gradient);
    color: #ffffff;
    border: 2px solid transparent;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.7),
        0 0 30px rgba(255, 107, 53, 0.5),
        0 0 45px rgba(255, 107, 53, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--fc-text-primary);
    border: 2px solid rgba(0, 191, 255, 0.5);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-secondary:hover {
    background: var(--fc-gradient-primary);
    color: var(--fc-text-dark);
    border-color: var(--fc-orange);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 3;
}

.slider-prev,
.slider-next {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider-prev:hover,
.slider-next:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* Slider Dots */
.slider-dots {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 3;
}

.slider-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.slider-dot.active,
.slider-dot:hover {
    background: white;
    transform: scale(1.2);
}

/* Legacy hero styles for fallback */
.hero {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    text-align: center;
    padding: 4rem 0;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: #000000;
    color: #ffffff;
    text-decoration: none;
    border-radius: 8px;
    border: 2px solid #0049a3;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow:
        0 0 10px rgba(0, 73, 163, 0.5),
        0 0 20px rgba(0, 73, 163, 0.3),
        0 0 30px rgba(0, 73, 163, 0.1);
    text-shadow: none;
}

.btn:hover {
    background: var(--fc-orange-gradient);
    color: #ffffff;
    border: 2px solid transparent;
    box-shadow:
        0 0 15px rgba(255, 107, 53, 0.7),
        0 0 30px rgba(255, 107, 53, 0.5),
        0 0 45px rgba(255, 107, 53, 0.3);
    transform: translateY(-2px);
}

.btn-white {
    background: rgba(255, 255, 255, 0.9);
    color: var(--fc-black);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-white:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

/* Competition cards */
.competitions-section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.competition-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 cards per row */
    gap: 2rem;
    margin: 2rem 0;
    justify-content: center;
    place-items: center;
}

.competition-card {
    background: var(--fc-bg-card);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 191, 255, 0.2);
    backdrop-filter: blur(10px);
}

.competition-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 191, 255, 0.3);
    border-color: var(--fc-blue-electric);
    background: var(--fc-bg-card-hover);
}

.competition-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card-title a {
    color: var(--fc-text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.card-title a:hover {
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.card-price {
    font-size: 1.5rem;
    font-weight: bold;
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.card-description {
    color: var(--fc-text-secondary);
    margin-bottom: 1rem;
}

/* Progress bar */
.progress-container {
    margin: 1rem 0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--fc-text-secondary);
    margin-bottom: 0.5rem;
}

/* Duplicate progress bar styles removed - using styles from line 124 instead */
    min-width: 1px;
}

/* Card actions */
.card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.card-actions .btn {
    width: 100%;
    text-align: center;
}

.time-remaining {
    font-size: 0.875rem;
    color: #6b7280;
}

/* How It Works Section */
.how-it-works-section {
    background: #f5f5f0;
    padding: 4rem 0;
}

.how-it-works-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 3rem;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.how-it-works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.how-it-works-card {
    background: var(--fc-black);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.how-it-works-card::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--fc-gradient-primary);
}

.how-it-works-card:hover {
    transform: translateY(-8px);
    border-color: var(--fc-orange);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.how-it-works-icon {
    color: #d4a574;
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.how-it-works-step-title {
    color: var(--fc-text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.how-it-works-description {
    color: var(--fc-text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Draw Transparency Info */
.draw-transparency-info {
    margin-top: 3rem;
    text-align: center;
    background: var(--fc-black);
    padding: 3rem 2.5rem;
    border-radius: 12px;
    border: 2px solid var(--fc-blue);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
}

.draw-transparency-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--fc-gradient-primary);
}

.draw-transparency-info h3 {
    color: var(--fc-blue);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.draw-transparency-info p {
    color: var(--fc-text-primary);
    line-height: 1.7;
    font-size: 1.1rem;
    margin: 0;
    font-weight: 400;
}

/* No Competitions Message Styling */
.competition-grid > div[style*="grid-column: 1 / -1"],
.competitions-grid > div[style*="grid-column: 1 / -1"] {
    grid-column: 1 / -1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 4rem 2rem !important;
    background: var(--fc-bg-card) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(0, 191, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    margin: 2rem auto !important;
    max-width: 600px !important;
    width: 100% !important;
}

.competition-grid > div[style*="grid-column: 1 / -1"] h3,
.competitions-grid > div[style*="grid-column: 1 / -1"] h3 {
    color: var(--fc-text-primary) !important;
    font-size: 2rem !important;
    margin-bottom: 1rem !important;
    background: var(--fc-gradient-primary) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    text-align: center !important;
}

.competition-grid > div[style*="grid-column: 1 / -1"] p,
.competitions-grid > div[style*="grid-column: 1 / -1"] p {
    color: var(--fc-text-secondary) !important;
    font-size: 1.1rem !important;
    margin: 0 !important;
    line-height: 1.6 !important;
    text-align: center !important;
}

/* Footer */
.site-footer {
    background: var(--fc-gradient-space);
    color: var(--fc-text-primary);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(0, 191, 255, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--fc-text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-section a:hover {
    color: var(--fc-yellow);
    text-shadow: 0 0 8px rgba(255, 211, 63, 0.5);
}

.footer-bottom {
    border-top: 1px solid rgba(0, 191, 255, 0.2);
    padding-top: 2rem;
    text-align: center;
    color: var(--fc-text-muted);
}

/* Single competition page */
.competition-detail {
    padding: 2rem 0;
}

.competition-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.competition-main {
    background: var(--fc-bg-card);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 191, 255, 0.2);
    backdrop-filter: blur(10px);
}

.competition-sidebar {
    background: var(--fc-bg-card);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 191, 255, 0.2);
    backdrop-filter: blur(10px);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--fc-black) 0%, #1a1a1a 100%);
    color: white;
    text-align: center;
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 191, 255, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.page-header .container {
    position: relative;
    z-index: 1;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: var(--fc-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.page-header p {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Filters Section */
.filters-section {
    background: var(--fc-bg-card);
    border: 1px solid rgba(0, 191, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filters-section .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.filters {
    display: flex;
    gap: 0.75rem;
    flex-wrap: nowrap;
}

.filter-btn {
    padding: 0.75rem 1.25rem;
    border: 2px solid rgba(0, 191, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: var(--fc-text-primary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(5px);
}

.filter-btn:hover {
    border-color: var(--fc-blue);
    background: rgba(0, 191, 255, 0.1);
    color: var(--fc-blue);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.2);
}

.filter-btn.active {
    border-color: var(--fc-blue);
    background: var(--fc-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 191, 255, 0.3);
}

.sort-options {
    margin-top: 0;
}

.sort-options select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(0, 191, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--fc-text-primary);
    min-width: 180px;
    font-size: 0.875rem;
    backdrop-filter: blur(5px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-options select:hover,
.sort-options select:focus {
    border-color: var(--fc-blue);
    background: rgba(0, 191, 255, 0.1);
    outline: none;
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.2);
}

/* Responsive filters */
@media (max-width: 768px) {
    .filters-section .container {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .filters {
        justify-content: center;
        flex-wrap: wrap;
    }

    .sort-options {
        text-align: center;
    }

    .sort-options select {
        width: 100%;
        max-width: 300px;
    }
}

/* No Competitions Message */
.no-competitions {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    background: var(--fc-bg-card);
    border: 2px dashed rgba(0, 191, 255, 0.3);
    border-radius: 12px;
    margin: 2rem 0;
}

.no-competitions h3 {
    font-size: 1.5rem;
    color: var(--fc-text-primary);
    margin-bottom: 1rem;
    font-weight: 600;
}

.no-competitions p {
    color: var(--fc-text-secondary);
    font-size: 1rem;
    margin: 0;
}

/* Filter Notice */
.filter-notice {
    grid-column: 1 / -1;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 8px;
    text-align: center;
}

.filter-notice p {
    margin: 0;
    color: #856404;
    font-size: 0.9rem;
}

/* Entry Method Tabs */
.entry-tab {
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.entry-tab:hover {
    background: #f59e0b !important;
    transform: translateY(-1px);
}

.entry-tab.active {
    background: #f59e0b !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content {
    transition: all 0.3s ease;
}

.tab-content h4 {
    font-weight: 600;
}

.tab-content ol li,
.tab-content ul li {
    line-height: 1.5;
}

/* Competition Messages */
.competition-message {
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.3s ease-out;
}

.competition-message-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
}

.competition-message-text {
    flex: 1;
    font-weight: 500;
    font-size: 0.95rem;
}

.competition-message-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    margin-left: 1rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.competition-message-close:hover {
    opacity: 1;
}

.competition-message-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    color: #155724;
}

.competition-message-success .competition-message-close {
    color: #155724;
}

.competition-message-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.competition-message-error .competition-message-close {
    color: #721c24;
}

.competition-message-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.competition-message-info .competition-message-close {
    color: #0c5460;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Order Thank You Page Styles */
.woocommerce-order {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
}

.order-success-header {
    animation: slideInDown 0.6s ease-out;
}

.order-summary-card,
.instant-wins-section,
.tickets-section,
.next-steps-section {
    animation: slideInDown 0.6s ease-out;
    animation-fill-mode: both;
}

.instant-wins-section {
    animation-delay: 0.1s;
}

.tickets-section {
    animation-delay: 0.2s;
}

.next-steps-section {
    animation-delay: 0.3s;
}

.action-buttons {
    animation: slideInDown 0.6s ease-out;
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn-primary:hover {
    background: #003d8a !important;
}

.action-buttons .btn-secondary:hover {
    background: #0049a3 !important;
    color: white !important;
}

.action-buttons .btn-wallet:hover {
    background: #218838 !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .woocommerce-order {
        padding: 0.5rem;
    }

    .order-success-header {
        padding: 1.5rem !important;
    }

    .order-success-header h1 {
        font-size: 1.5rem !important;
    }

    .order-summary-card div[style*="grid-template-columns"] {
        grid-template-columns: 1fr 1fr !important;
        gap: 1rem !important;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .action-buttons .btn {
        min-width: auto !important;
        width: 100%;
    }

    .next-steps-section div[style*="display: flex"] {
        flex-direction: column !important;
        text-align: center;
    }

    .next-steps-section div[style*="flex-shrink: 0"] {
        align-self: center !important;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .order-summary-card div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }

    .tickets-section div[style*="display: flex; flex-wrap: wrap"] {
        justify-content: center !important;
    }
}

/* Competition Card Badges */
.badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 10;
}

.ending-soon-badge {
    background: #ef4444;
    color: white;
}

.status-badge {
    background: #6b7280;
    color: white;
}

.completed-badge {
    background: #10b981;
    color: white;
}

.awaiting-badge {
    background: #f59e0b;
    color: white;
}

/* Competition Card States */
.competition-card.inactive {
    opacity: 0.7;
}

.competition-card.ending-soon {
    border: 2px solid #ef4444;
}

.competition-card.completed {
    border: 2px solid #10b981;
}

.competition-card.awaiting_draw {
    border: 2px solid #f59e0b;
}

.competition-card.active {
    border: 2px solid #0049a3;
}

.competition-card.inactive {
    border: 2px solid #6b7280;
    opacity: 0.7;
}

/* Status Badge Styling */
.status-badge.awaiting-draw {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-badge.completed {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-badge.active {
    background: linear-gradient(135deg, #0049a3, #003875);
    color: white;
}

/* Progress Bar Variants */
.progress-fill.completed {
    background: #10b981;
}

.progress-fill.awaiting {
    background: #f59e0b;
}

/* Winner Info */
.winner-info {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
}

.winner-badge {
    background: #10b981;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.winner-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.ticket-number,
.draw-date {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Draw Info */
.draw-info {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    font-size: 0.875rem;
    color: #92400e;
}

.draw-info strong {
    color: #92400e;
}

.draw-info span {
    color: #92400e;
}

.scheduled-draw,
.pending-schedule,
.ended-info {
    margin-bottom: 0.5rem;
}

.scheduled-draw:last-child,
.pending-schedule:last-child,
.ended-info:last-child {
    margin-bottom: 0;
}

/* Winners Grid */
.winners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.winner-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.winner-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.winner-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
}

.winner-name {
    font-weight: bold;
    color: #10b981;
    margin-bottom: 0.5rem;
}

.winner-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Button Variants */
.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    color: #6b7280;
    text-decoration: none;
    border-radius: 6px;
}

.pagination a:hover {
    background: #f3f4f6;
}

.pagination .current {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

/* Responsive - Additional mobile styles */
@media (max-width: 768px) {
    .hero h1,
    .page-header h1,
    .slide-title {
        font-size: 2rem;
    }

    /* Hero slider responsive */
    .hero-slider {
        height: 400px;
        border-top: 4px solid var(--fc-blue-electric);
        border-bottom: 4px solid var(--fc-blue-electric);
        box-shadow:
            0 -6px 15px rgba(0, 191, 255, 0.4),
            0 6px 15px rgba(0, 191, 255, 0.4);
    }

    .slide-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    /* How It Works responsive */
    .how-it-works-title {
        font-size: 2rem;
    }

    /* Draw Transparency responsive */
    .draw-transparency-info {
        padding: 2rem 1.5rem;
        margin-top: 2rem;
    }

    .draw-transparency-info h3 {
        font-size: 1.5rem;
    }

    .draw-transparency-info p {
        font-size: 1rem;
    }

    .how-it-works-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .how-it-works-card {
        padding: 2rem 1.5rem;
    }

    .slide-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .slide-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .slider-nav {
        padding: 0 1rem;
    }

    .slider-prev,
    .slider-next {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .competition-layout {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }

    /* Competition info bars responsive */
    .info-bars-grid {
        grid-template-columns: 1fr;
        gap: 1px;
    }

    .info-bar {
        padding: 0.75rem;
        font-size: 0.8rem;
        border-right: none;
        border-bottom: 1px solid #333;
    }

    .info-bar:last-child {
        border-bottom: none;
    }

    .info-text {
        white-space: normal;
        text-align: center;
    }

    .filters {
        flex-wrap: wrap;
    }

    .winners-grid {
        grid-template-columns: 1fr;
    }

    .winner-card {
        flex-direction: column;
        text-align: center;
    }
}

/* URGENT FIXES - Cache Buster v3 - Force form alignment */
/* Force form field alignment with highest specificity */
body.woocommerce-account .woocommerce-account .woocommerce-form-row input[type="text"],
body.woocommerce-account .woocommerce-account .woocommerce-form-row input[type="email"],
body.woocommerce-account .woocommerce-account .woocommerce-form-row input[type="password"],
body.woocommerce-account .woocommerce-account .woocommerce-form-row input[type="tel"],
body.woocommerce-account .woocommerce-account .woocommerce-form-row select {
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 1rem 1.25rem !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    background: #f8f9fa !important;
}

/* Removed conflicting rules - using standard WooCommerce float layout */

/* Header consistency - removed conflicting WooCommerce overrides to use standard header */



/* WooCommerce Main Template Styling */
.woocommerce-main {
    padding: 3rem 0;
    min-height: 60vh;
    background: var(--fc-bg-primary);
}

.account-breadcrumb {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    margin-bottom: 2rem;
}

.account-breadcrumb nav {
    color: #6b7280;
    font-size: 0.875rem;
}

.account-breadcrumb a {
    color: var(--fc-primary);
    text-decoration: none;
}

.account-breadcrumb a:hover {
    text-decoration: underline;
}

.account-header {
    text-align: center;
    margin-bottom: 3rem;
}

.account-header h1 {
    font-size: 3rem;
    font-weight: bold;
    color: var(--fc-text-primary);
    margin-bottom: 1rem;
}

.shop-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.shop-header .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.shop-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--fc-text-primary);
    margin: 0;
}

/* Single Product Page Styling */
.single-product-wrapper {
    background: #f8f9fa;
    min-height: 60vh;
    padding: 2rem 0;
}

.single-product-wrapper .container {
    max-width: 1500px;
    margin: 0 auto;
    padding: 0 3rem;
}

.breadcrumb-nav {
    margin-bottom: 2rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.breadcrumb-nav a {
    color: var(--fc-primary);
    text-decoration: none;
}

.breadcrumb-nav a:hover {
    text-decoration: underline;
}

/* Competition Product Layout - Guitar Gear Giveaway Style */
.competition-product-layout {
    display: grid;
    grid-template-columns: 30% 70%;
    gap: 2rem;
    margin-bottom: 3rem;
    align-items: start;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 3rem auto;
}

/* Left Side: Gallery (40% width) */
.competition-gallery {
    width: 100%;
}

.main-product-image {
    width: 100%;
    margin-bottom: 1rem;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
}

.main-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.thumbnail-gallery {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.thumbnail-item {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    border: 2px solid transparent;
    cursor: pointer;
}

.thumbnail-item.active {
    border-color: #0049a3;
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Right Side: Competition Details (60% width) */
.competition-details {
    background: white;
    padding: 0;
    width: 100%;
}

.product-gallery-section {
    position: relative;
    width: 400px;
    max-width: 400px;
}

/* Ensure WooCommerce gallery fits properly */
.product-gallery-section .woocommerce-product-gallery {
    width: 100%;
    max-width: 400px;
}

.product-gallery-section .woocommerce-product-gallery__wrapper {
    width: 100%;
}

.product-gallery-section .woocommerce-product-gallery__image {
    width: 100%;
    margin: 0;
}

.product-gallery-section .woocommerce-product-gallery__image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* Override WooCommerce default single product layout */
.woocommerce div.product div.summary {
    width: 100% !important;
    float: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.woocommerce div.product div.images {
    width: 100% !important;
    float: none !important;
    margin: 0 !important;
}

.woocommerce div.product {
    display: block !important;
}

/* Hide default WooCommerce elements we're replacing */
.woocommerce div.product .summary .entry-title {
    display: none;
}

.woocommerce div.product .summary .price {
    display: none;
}

.product-details-section {
    background: white;
    border-radius: 12px;
    padding: 0;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    width: 100%;
    min-height: 600px;
}

/* Competition Banner */
.competition-banner {
    background: linear-gradient(135deg, var(--fc-primary), var(--fc-secondary));
    color: white;
    text-align: center;
    padding: 1rem;
    margin-bottom: 0;
}

.banner-text {
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* Price Section */
.price-section {
    text-align: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 0.25rem;
}

.price-amount .price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin: 0;
}

.price-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 400;
}

/* Countdown Section */
.countdown-section {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.countdown-unit {
    background: #000;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    min-width: 50px;
    text-align: center;
}

.countdown-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
}

.countdown-label {
    display: block;
    font-size: 0.7rem;
    font-weight: 400;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.countdown-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #666;
}

.draw-date {
    font-size: 0.85rem;
    color: #666;
}

/* Orange Banner */
.orange-banner {
    background: linear-gradient(135deg, #ff8c00, #ff7700);
    color: white;
    padding: 1rem 1.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.95rem;
}

.banner-icon {
    margin-right: 0.5rem;
}

/* Skill Question Section */
.skill-question-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.question-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.answer-option {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.answer-option:hover {
    border-color: #0049a3;
    background: #f8f9ff;
}

.answer-option input[type="radio"] {
    margin-right: 0.75rem;
    accent-color: #0049a3;
}

.answer-option span {
    font-size: 0.95rem;
    color: #333;
}

/* Entries Section */
.entries-section {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.entries-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.entries-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.entry-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.entry-btn:hover {
    background: #e9ecef;
    border-color: #0049a3;
}

.entry-count {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    min-width: 40px;
    text-align: center;
}

.entry-slider-container {
    margin-top: 1rem;
}

.entry-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.entry-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0049a3;
    cursor: pointer;
}

.entry-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #0049a3;
    cursor: pointer;
    border: none;
}

/* Add to Basket Button */
.add-to-cart-section {
    padding: 1.5rem;
    text-align: center;
}

.add-to-basket-btn {
    background: #000;
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 100%;
    max-width: 300px;
}

.add-to-basket-btn:hover:not(:disabled) {
    background: #333;
}

.add-to-basket-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Free Entry Section */
/* Entry Tabs Section */
.entry-tabs-section {
    margin-top: 1.5rem;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
    background: #000;
}

.entry-tabs {
    display: flex;
    background: #000;
    border-bottom: 1px solid #333;
}

.entry-tab {
    flex: 1;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    font-weight: 600;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.entry-tab:hover {
    background: #1a1a1a;
    color: #fff;
}

.entry-tab.active {
    background: rgba(0, 73, 163, 0.9);
    color: #fff;
    border-bottom-color: #0049a3;
    box-shadow: 0 0 15px rgba(0, 73, 163, 0.4);
}

.entry-content {
    background: #1a1a1a;
}

.entry-tab-content {
    display: none;
    padding: 1.5rem;
}

.entry-tab-content.active {
    display: block;
}

.entry-tab-content h4 {
    color: #ff6b35;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.entry-tab-content p {
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.entry-benefits {
    margin-top: 1rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.benefit-icon {
    color: #ff6b35;
    font-weight: bold;
    font-size: 1rem;
}

.free-entry-address {
    background: #333;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #ff6b35;
    font-style: normal;
    line-height: 1.5;
    margin: 1rem 0;
    color: #ffffff;
}

.free-entry-address strong {
    color: #ff6b35;
}

/* Instant Wins Section */
.instant-wins-section {
    padding: 1.5rem;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    margin-top: 1.5rem;
}

.instant-wins-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ff6b35;
    margin-bottom: 0.75rem;
    text-align: center;
}

.instant-wins-section p {
    font-size: 0.9rem;
    color: #ffffff;
    margin-bottom: 1rem;
    text-align: center;
}

.instant-win-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #333;
    border-radius: 6px;
    border: 1px solid #555;
    margin-bottom: 1rem;
}

.instant-win-details {
    flex: 1;
}

.instant-win-details strong {
    color: #ff6b35;
    font-size: 1rem;
}

.instant-win-type {
    color: #ffffff;
    font-size: 0.85rem;
}

.instant-win-details p {
    margin: 0.5rem 0 0 0;
    font-size: 0.85rem;
    color: #ffffff;
    text-align: left;
}

.instant-win-quantity {
    display: flex;
    align-items: center;
}

.quantity-badge {
    background: var(--fc-orange-gradient);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.instant-wins-section small {
    display: block;
    text-align: center;
    color: #ffffff;
    font-size: 0.8rem;
    margin-top: 1rem;
}

.win-tickets {
    font-size: 0.85rem;
    color: #ffffff;
}

/* Skill Question Section */
.skill-question-section {
    margin: 0 2rem 2rem 2rem;
    padding: 1.5rem;
    background: #1a1a1a;
    border-radius: 8px;
    border: 2px solid #ff6b35;
}

.skill-question-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #ff6b35;
    margin-bottom: 1rem;
    text-align: center;
}

.question-container {
    background: #333;
    padding: 1.5rem;
    border-radius: 6px;
    border: 1px solid #555;
}

.question-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 1rem;
    text-align: center;
}

.answer-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.answer-option {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #555;
    border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #ffffff;
}

.answer-option:hover {
    background: #666;
    border-color: #ff6b35;
}

.answer-option input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.answer-option input[type="radio"]:checked + span {
    font-weight: 600;
    color: #ff6b35;
}

.answer-option:has(input[type="radio"]:checked) {
    background: #e3f2fd;
    border-color: #2196f3;
}

/* Ticket Selection */
.ticket-selection-section {
    margin: 0 2rem 2rem 2rem;
    padding: 1.5rem;
    background: #1a1a1a;
    border-radius: 8px;
    border: 1px solid #333;
}

.ticket-selection-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #ff6b35;
    margin-bottom: 1rem;
    text-align: center;
}

.ticket-slider-container {
    margin-bottom: 1.5rem;
}

.ticket-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #555;
    outline: none;
    margin-bottom: 1rem;
    -webkit-appearance: none;
}

.ticket-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--fc-orange-gradient);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ticket-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--fc-orange-gradient);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ticket-display {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
}

#ticket-count {
    color: #ff6b35;
    font-size: 1.5rem;
}

/* Instant Wins Section */
.instant-wins-section {
    margin: 0 2rem 2rem 2rem;
    padding: 1.5rem;
    background: #1a1a1a;
    border-radius: 8px;
    border: 2px solid #ff6b35;
    text-align: center;
}

.instant-wins-section h4 {
    color: #ff6b35;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.instant-wins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.instant-win-item {
    background: #333;
    border-radius: 8px;
    padding: 1rem;
    border: 2px solid #555;
    transition: transform 0.2s ease;
}

.instant-win-item:hover {
    transform: translateY(-2px);
    border-color: #ff6b35;
    box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
}

.instant-win-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.instant-win-text {
    font-weight: 600;
    color: #ff6b35;
    font-size: 0.9rem;
}

.instant-wins-note {
    color: #ffffff;
    font-size: 0.85rem;
    margin: 0;
    font-style: italic;
}

/* Add to Cart Section */
.add-to-cart-section {
    text-align: center;
    margin-top: 1.5rem;
}

.competition-add-to-cart {
    background: linear-gradient(135deg, #0049a3, #003d8a);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 100%;
    max-width: 300px;
}

.competition-add-to-cart:hover:not(:disabled) {
    background: linear-gradient(135deg, #003d8a, #002d6b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 73, 163, 0.3);
}

.competition-add-to-cart:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.add-to-cart-section .cart {
    margin: 0;
}

.add-to-cart-section .quantity {
    display: none; /* Hide quantity selector since we're using slider */
}

/* Free Entry Section */
.free-entry-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border-left: 4px solid var(--fc-primary);
    margin: 0 2rem 2rem 2rem;
}

.free-entry-section h4 {
    color: var(--fc-text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.free-entry-section p {
    color: var(--fc-text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.free-entry-section address {
    font-style: normal;
    color: var(--fc-text-secondary);
    line-height: 1.4;
    font-size: 0.85rem;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

/* Competition Info Section */
.competition-info-section {
    margin-top: 2rem;
    padding: 2rem;
    border-top: 1px solid #e9ecef;
}

.competition-description {
    color: var(--fc-text-secondary);
    line-height: 1.6;
}

.competition-description p {
    margin-bottom: 1rem;
}

/* WooCommerce Product Images */
.woocommerce-product-gallery {
    margin-bottom: 0;
}

.woocommerce-product-gallery__wrapper {
    border-radius: 12px;
    overflow: hidden;
}

.woocommerce-product-gallery__image img {
    width: 100%;
    height: auto;
    display: block;
}

/* Add to Cart Form */
.cart {
    margin: 2rem 0;
}

.quantity {
    margin-right: 1rem;
    display: inline-block;
}

.quantity input {
    width: 80px;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
}

.single_add_to_cart_button {
    background: linear-gradient(135deg, var(--fc-primary), var(--fc-secondary));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.single_add_to_cart_button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 73, 163, 0.3);
}

/* Product Meta */
.product_meta {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.product_meta span {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--fc-text-secondary);
    font-size: 0.875rem;
}

.product_meta a {
    color: var(--fc-primary);
    text-decoration: none;
}

.product_meta a:hover {
    text-decoration: underline;
}

/* Stock Status */
.stock {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 1rem;
}

.out-of-stock {
    color: #dc3545;
}

/* Price Styling */
.price {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--fc-primary);
    margin-bottom: 1rem;
}

.price del {
    color: #6c757d;
    margin-right: 0.5rem;
}

.price ins {
    text-decoration: none;
    color: var(--fc-primary);
}

/* Product Tabs */
.woocommerce-tabs {
    margin-top: 3rem;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.woocommerce-tabs .tabs {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
}

.woocommerce-tabs .tabs li {
    margin: 0;
}

.woocommerce-tabs .tabs li a {
    display: block;
    padding: 1rem 2rem;
    color: var(--fc-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.woocommerce-tabs .tabs li.active a,
.woocommerce-tabs .tabs li a:hover {
    background: white;
    color: var(--fc-primary);
    border-bottom: 2px solid var(--fc-primary);
}

.woocommerce-tabs .panel {
    padding: 2rem;
}

/* WooCommerce Notices */
.woocommerce-notices-wrapper {
    margin-bottom: 2rem;
}

.woocommerce-message,
.woocommerce-info,
.woocommerce-error {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.woocommerce-message {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    color: #155724;
}

.woocommerce-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.woocommerce-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Product Rating */
.woocommerce-product-rating {
    margin-bottom: 1rem;
}

.star-rating {
    color: #fbbf24;
    font-size: 1rem;
}

/* Related Products */
.related.products {
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 1px solid #e9ecef;
}

.related.products h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--fc-text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.related.products .products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .competition-product-layout {
        grid-template-columns: 35% 65%;
        gap: 1.5rem;
    }

    .competition-gallery {
        width: 100%;
    }

    .single-product-wrapper .container {
        padding: 0 2rem;
    }
}

@media (max-width: 768px) {
    .competition-product-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .competition-gallery {
        width: 100%;
    }

    .single-product-wrapper .container {
        padding: 0 1rem;
    }

    .price-amount {
        font-size: 2rem;
    }

    .countdown-timer {
        gap: 0.25rem;
    }

    .countdown-unit {
        padding: 0.5rem 0.75rem;
        min-width: 45px;
    }

    .countdown-number {
        font-size: 1.25rem;
    }

    .entries-controls {
        gap: 0.75rem;
    }

    .entry-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .entry-count {
        font-size: 1.25rem;
    }

    .add-to-basket-btn {
        font-size: 0.9rem;
        padding: 0.875rem 1.5rem;
    }
}

/* Order Tickets Section */
.order-tickets-section {
    margin-top: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.order-tickets-section h2 {
    color: #0049a3;
    margin-bottom: 25px;
    font-size: 1.8em;
    text-align: center;
}

.competition-tickets {
    background: white;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.competition-tickets h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.ticket-numbers {
    background: #e8f4fd;
    border-radius: 4px;
    padding: 15px;
    border-left: 4px solid #0049a3;
}

.ticket-numbers p {
    margin: 0;
    color: #333;
    font-size: 1.1em;
}

/* My Account Tickets */
.tickets-list {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.ticket-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #dee2e6;
    transition: box-shadow 0.3s ease;
}

.ticket-item:hover {
    box-shadow: 0 4px 12px rgba(0, 73, 163, 0.1);
}

.ticket-item.completed {
    border-left: 4px solid #28a745;
}

.ticket-item.active {
    border-left: 4px solid #0049a3;
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.ticket-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2em;
}

.ticket-number {
    background: #0049a3;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
}

.ticket-details p {
    margin: 5px 0;
    color: #666;
    font-size: 0.95em;
}

.ticket-details strong {
    color: #333;
}

/* Legal Document Styles */
.legal-document {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
}

.legal-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 3px solid #0049a3;
}

.legal-header h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.legal-header .last-updated {
    color: #6b7280;
    font-style: italic;
    font-size: 0.9rem;
}

.company-info-box {
    background: linear-gradient(135deg, #0049a3 0%, #0056c7 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 3rem;
    text-align: center;
}

.company-info-box h2 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.company-details {
    font-size: 1.1rem;
    line-height: 1.8;
}

.legal-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #0049a3;
}

.legal-section h2 {
    color: #1f2937;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.legal-section h3 {
    color: #374151;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.subsection {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.definition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.definition-item {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.definition-item h3 {
    color: #0049a3;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.numbered-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.list-item {
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item-number {
    background: #0049a3;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    min-width: 3rem;
    text-align: center;
    height: fit-content;
}

.item-content {
    flex: 1;
}

.item-content strong {
    color: #1f2937;
    display: block;
    margin-bottom: 0.5rem;
}

.free-entry-box {
    background: #f0f9ff;
    border: 2px solid #0ea5e9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.address-box {
    background: white;
    border: 1px solid #0ea5e9;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: center;
    font-weight: 500;
}

.free-entry-box ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.free-entry-box li {
    margin-bottom: 0.5rem;
}

.legal-footer {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e5e7eb;
    color: #6b7280;
}

/* Legal Document Responsive Styles */
@media (max-width: 768px) {
    .legal-document {
        padding: 1rem;
        margin: 1rem;
    }

    .legal-header h1 {
        font-size: 2rem;
    }

    .definition-grid {
        grid-template-columns: 1fr;
    }

    .list-item {
        flex-direction: column;
        gap: 1rem;
    }

    .item-number {
        align-self: flex-start;
        min-width: auto;
        width: fit-content;
    }

    .legal-section {
        padding: 1.5rem;
    }

    .company-info-box {
        padding: 1.5rem;
    }
}
