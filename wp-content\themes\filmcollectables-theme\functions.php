<?php
/**
 * Film Collectables Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function filmcollectables_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // WooCommerce support
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'filmcollectables'),
        'footer' => __('Footer Menu', 'filmcollectables'),
    ));
}
add_action('after_setup_theme', 'filmcollectables_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function filmcollectables_scripts() {
    // Google Fonts - Roboto
    wp_enqueue_style('google-fonts-roboto', 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,400;1,500;1,700;1,900&display=swap', array(), null);

    // Theme stylesheet
    wp_enqueue_style('filmcollectables-style', get_stylesheet_uri(), array('google-fonts-roboto'), wp_get_theme()->get('Version'));

    // jQuery (WordPress includes this)
    wp_enqueue_script('jquery');

    // WooCommerce cart fragments (for AJAX cart updates)
    if (class_exists('WooCommerce')) {
        wp_enqueue_script('wc-cart-fragments');
    }

    // Theme JavaScript
    wp_enqueue_script('filmcollectables-theme', get_template_directory_uri() . '/js/theme.js', array('jquery', 'wc-cart-fragments'), wp_get_theme()->get('Version'), true);

    // Localize script for AJAX
    wp_localize_script('filmcollectables-theme', 'filmcollectables_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('filmcollectables_nonce'),
        'check_tickets_nonce' => wp_create_nonce('check_order_tickets')
    ));

    // Hero slider script (only on front page)
    if (is_front_page()) {
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                // Hero Slider Functionality
                let currentSlide = 0;
                const slides = $(".hero-slide");
                const dots = $(".slider-dot");
                const totalSlides = slides.length;

                if (totalSlides <= 1) return; // No need for slider if only one slide

                function showSlide(index) {
                    slides.removeClass("active");
                    dots.removeClass("active");

                    slides.eq(index).addClass("active");
                    dots.eq(index).addClass("active");

                    currentSlide = index;
                }

                function nextSlide() {
                    const next = (currentSlide + 1) % totalSlides;
                    showSlide(next);
                }

                function prevSlide() {
                    const prev = (currentSlide - 1 + totalSlides) % totalSlides;
                    showSlide(prev);
                }

                // Navigation buttons
                $(".slider-next").click(nextSlide);
                $(".slider-prev").click(prevSlide);

                // Dot navigation
                $(".slider-dot").click(function() {
                    const slideIndex = $(this).data("slide") - 1;
                    showSlide(slideIndex);
                });

                // Auto-advance slider every 5 seconds
                setInterval(nextSlide, 5000);

                // Pause auto-advance on hover
                $(".hero-slider").hover(
                    function() { clearInterval(autoSlide); },
                    function() { autoSlide = setInterval(nextSlide, 5000); }
                );
            });
        ');
    }
}
add_action('wp_enqueue_scripts', 'filmcollectables_scripts');





/**
 * WooCommerce Cart Fragments
 * Updates cart count and total via AJAX
 */
function filmcollectables_cart_fragments($fragments) {
    if (class_exists('WooCommerce')) {
        $cart_count = WC()->cart->get_cart_contents_count();
        $cart_total = WC()->cart->get_cart_total();

        // Update cart count
        $fragments['.cart-count'] = '<span class="cart-count">' . $cart_count . '</span>';

        // Update cart total (get subtotal without currency symbol)
        $cart_subtotal = WC()->cart->get_cart_subtotal();
        $fragments['.cart-total'] = '<span class="cart-total">' . $cart_subtotal . '</span>';

        // Update entire cart icon if needed
        ob_start();
        ?>
        <a href="<?php echo esc_url(wc_get_cart_url()); ?>" class="cart-icon" id="cart-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="9" cy="21" r="1"></circle>
                <circle cx="20" cy="21" r="1"></circle>
                <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
            <span class="cart-count"><?php echo $cart_count; ?></span>
            <span class="cart-total"><?php echo WC()->cart->get_cart_subtotal(); ?></span>
        </a>
        <?php
        $fragments['.cart-icon'] = ob_get_clean();

        // Update wallet balance in header if user is logged in
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $wallet_balance = get_user_wallet_balance($user_id);

            // Update desktop wallet link
            $fragments['a[href*="/wallet/"].header-link'] = '<a href="' . esc_url(home_url('/wallet/')) . '" class="header-link">
                Wallet (£' . number_format($wallet_balance, 2) . ')
            </a>';

            // Update mobile wallet link
            $fragments['a[href*="/wallet/"].mobile-nav-link'] = '<a href="' . esc_url(home_url('/wallet/')) . '" class="mobile-nav-link">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                    <line x1="1" y1="10" x2="23" y2="10"></line>
                </svg>
                Wallet (£' . number_format($wallet_balance, 2) . ')
            </a>';
        }
    }

    return $fragments;
}
add_filter('woocommerce_add_to_cart_fragments', 'filmcollectables_cart_fragments');

/**
 * Custom Post Type Support
 * Note: Competition post type is now handled by WooCommerce products
 * with competition features enabled via the Competition Manager plugin
 */



/**
 * User Wallet System
 */
function get_user_wallet_balance($user_id) {
    // Aggressively clear all possible caches
    wp_cache_delete($user_id, 'user_meta');
    wp_cache_delete($user_id, 'users');
    wp_cache_delete("wallet_balance_$user_id", 'user_meta');

    // Check for and clean up duplicate entries
    cleanup_duplicate_wallet_entries($user_id);

    // Get the wallet balance directly from database
    global $wpdb;
    $balance = $wpdb->get_var($wpdb->prepare(
        "SELECT meta_value FROM {$wpdb->usermeta}
         WHERE user_id = %d AND meta_key = 'wallet_balance'
         ORDER BY umeta_id DESC LIMIT 1",
        $user_id
    ));

    // If no balance found, return 0
    if ($balance === null) {
        return 0.00;
    }

    return floatval($balance);
}

/**
 * Debug function to check wallet balance consistency
 */
function debug_wallet_balance($user_id) {
    global $wpdb;

    // Get all wallet_balance entries for this user
    $all_balances = $wpdb->get_results($wpdb->prepare(
        "SELECT umeta_id, meta_value, meta_key FROM {$wpdb->usermeta}
         WHERE user_id = %d AND meta_key = 'wallet_balance'
         ORDER BY umeta_id DESC",
        $user_id
    ));

    // Get using WordPress function
    $wp_balance = get_user_meta($user_id, 'wallet_balance', true);

    // Get using our function
    $our_balance = get_user_wallet_balance($user_id);

    error_log("WALLET DEBUG for user $user_id:");
    error_log("WordPress get_user_meta: " . $wp_balance);
    error_log("Our function: " . $our_balance);
    error_log("Database entries: " . print_r($all_balances, true));

    return array(
        'wp_balance' => $wp_balance,
        'our_balance' => $our_balance,
        'db_entries' => $all_balances
    );
}

/**
 * Clean up duplicate wallet balance entries
 */
function cleanup_duplicate_wallet_entries($user_id) {
    global $wpdb;

    // Get all wallet_balance entries for this user
    $all_balances = $wpdb->get_results($wpdb->prepare(
        "SELECT umeta_id, meta_value FROM {$wpdb->usermeta}
         WHERE user_id = %d AND meta_key = 'wallet_balance'
         ORDER BY umeta_id DESC",
        $user_id
    ));

    if (count($all_balances) > 1) {
        // Keep the most recent entry (first in DESC order)
        $keep_entry = array_shift($all_balances);

        // Delete the rest
        foreach ($all_balances as $entry) {
            $wpdb->delete(
                $wpdb->usermeta,
                array('umeta_id' => $entry->umeta_id),
                array('%d')
            );
        }

        error_log("Cleaned up " . count($all_balances) . " duplicate wallet entries for user $user_id. Kept balance: " . $keep_entry->meta_value);

        // Clear cache
        wp_cache_delete($user_id, 'user_meta');

        return $keep_entry->meta_value;
    }

    return false;
}

/**
 * One-time cleanup function for all users with duplicate wallet entries
 */
function cleanup_all_duplicate_wallet_entries() {
    if (!current_user_can('administrator')) {
        return;
    }

    global $wpdb;

    // Get all users who have wallet_balance entries
    $users_with_duplicates = $wpdb->get_results(
        "SELECT user_id, COUNT(*) as count
         FROM {$wpdb->usermeta}
         WHERE meta_key = 'wallet_balance'
         GROUP BY user_id
         HAVING count > 1"
    );

    $cleaned_count = 0;
    foreach ($users_with_duplicates as $user_data) {
        $cleaned = cleanup_duplicate_wallet_entries($user_data->user_id);
        if ($cleaned !== false) {
            $cleaned_count++;
        }
    }

    if ($cleaned_count > 0) {
        add_action('admin_notices', function() use ($cleaned_count) {
            echo '<div class="notice notice-success"><p>Cleaned up duplicate wallet entries for ' . $cleaned_count . ' users.</p></div>';
        });
    }
}

// Add admin action to trigger cleanup
add_action('wp_ajax_cleanup_wallet_duplicates', 'cleanup_all_duplicate_wallet_entries');

/**
 * Temporary debug function to check wallet balance in database
 */
function debug_current_wallet_balance() {
    if (!current_user_can('administrator')) {
        return;
    }

    $user_id = get_current_user_id();
    global $wpdb;

    // Check database directly
    $db_balance = $wpdb->get_var($wpdb->prepare(
        "SELECT meta_value FROM {$wpdb->usermeta}
         WHERE user_id = %d AND meta_key = 'wallet_balance'
         ORDER BY umeta_id DESC LIMIT 1",
        $user_id
    ));

    // Check WordPress function
    $wp_balance = get_user_meta($user_id, 'wallet_balance', true);

    // Check our function
    $our_balance = get_user_wallet_balance($user_id);

    echo "<div style='background: #fff; border: 1px solid #ccc; padding: 10px; margin: 10px; position: fixed; top: 50px; right: 10px; z-index: 9999; max-width: 300px;'>";
    echo "<h4>Wallet Debug for User $user_id:</h4>";
    echo "Database: " . ($db_balance ? "£" . number_format(floatval($db_balance), 2) : "NULL") . "<br>";
    echo "WordPress: " . ($wp_balance ? "£" . number_format(floatval($wp_balance), 2) : "NULL") . "<br>";
    echo "Our Function: £" . number_format($our_balance, 2) . "<br>";
    echo "<small><a href='?clear_debug=1'>Clear Debug</a></small>";
    echo "</div>";
}

// Add debug to admin pages only
if (is_admin() && current_user_can('administrator') && !isset($_GET['clear_debug'])) {
    add_action('admin_notices', 'debug_current_wallet_balance');
}

/**
 * Add JavaScript to handle wallet refresh on frontend
 */
function add_wallet_refresh_script() {
    if (isset($_COOKIE['force_wallet_refresh'])) {
        // Clear the cookie
        setcookie('force_wallet_refresh', '', time() - 3600, '/');

        // Add script to clear browser storage and refresh fragments
        echo '<script>
            // Clear any wallet-related browser storage
            if (typeof Storage !== "undefined") {
                // Clear sessionStorage
                for (var key in sessionStorage) {
                    if (key.includes("wallet") || key.includes("wc_cart") || key.includes("woocommerce")) {
                        sessionStorage.removeItem(key);
                    }
                }

                // Clear localStorage
                for (var key in localStorage) {
                    if (key.includes("wallet") || key.includes("wc_cart") || key.includes("woocommerce")) {
                        localStorage.removeItem(key);
                    }
                }
            }

            // Force refresh cart fragments when page loads
            jQuery(document).ready(function($) {
                if (typeof wc_cart_fragments_params !== "undefined") {
                    $(document.body).trigger("wc_fragment_refresh");

                    // Also refresh after a delay to ensure it takes effect
                    setTimeout(function() {
                        $(document.body).trigger("wc_fragment_refresh");
                    }, 500);
                }
            });
        </script>';
    }
}
add_action('wp_footer', 'add_wallet_refresh_script');

function add_to_user_wallet($user_id, $amount, $description = '') {
    $current_balance = get_user_wallet_balance($user_id);
    $new_balance = $current_balance + floatval($amount);

    // Delete any existing wallet_balance entries first to prevent duplicates
    global $wpdb;
    $wpdb->delete(
        $wpdb->usermeta,
        array('user_id' => $user_id, 'meta_key' => 'wallet_balance'),
        array('%d', '%s')
    );

    // Add the new balance
    add_user_meta($user_id, 'wallet_balance', $new_balance, true);

    // Clear cache
    wp_cache_delete($user_id, 'user_meta');

    // Log the transaction
    $transactions = get_user_meta($user_id, 'wallet_transactions', true) ?: array();
    $transactions[] = array(
        'amount' => floatval($amount),
        'type' => 'credit',
        'description' => $description,
        'balance_after' => $new_balance,
        'date' => current_time('mysql')
    );
    update_user_meta($user_id, 'wallet_transactions', $transactions);

    return $new_balance;
}

function deduct_from_user_wallet($user_id, $amount, $description = '') {
    $current_balance = get_user_wallet_balance($user_id);
    $amount = floatval($amount);

    if ($current_balance < $amount) {
        return false; // Insufficient funds
    }

    $new_balance = $current_balance - $amount;

    // Delete any existing wallet_balance entries first to prevent duplicates
    global $wpdb;
    $wpdb->delete(
        $wpdb->usermeta,
        array('user_id' => $user_id, 'meta_key' => 'wallet_balance'),
        array('%d', '%s')
    );

    // Add the new balance
    add_user_meta($user_id, 'wallet_balance', $new_balance, true);

    // Clear cache
    wp_cache_delete($user_id, 'user_meta');

    // Log the transaction
    $transactions = get_user_meta($user_id, 'wallet_transactions', true) ?: array();
    $transactions[] = array(
        'amount' => -$amount,
        'type' => 'debit',
        'description' => $description,
        'balance_after' => $new_balance,
        'date' => current_time('mysql')
    );
    update_user_meta($user_id, 'wallet_transactions', $transactions);

    return $new_balance;
}

function get_user_wallet_transactions($user_id, $limit = 10) {
    $transactions = get_user_meta($user_id, 'wallet_transactions', true) ?: array();
    return array_slice(array_reverse($transactions), 0, $limit);
}

/**
 * Create Standalone Wallet Page
 */
function create_wallet_page_rewrite() {
    add_rewrite_rule('^wallet/?$', 'index.php?wallet_page=1', 'top');
}
add_action('init', 'create_wallet_page_rewrite');

// Flush rewrite rules on theme activation
function flush_wallet_rewrite_rules() {
    create_wallet_page_rewrite();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_wallet_rewrite_rules');

// One-time flush for wallet page (remove after first load)
function force_flush_wallet_rules() {
    if (!get_option('wallet_rules_flushed')) {
        create_wallet_page_rewrite();
        flush_rewrite_rules();
        update_option('wallet_rules_flushed', true);
    }
}
add_action('init', 'force_flush_wallet_rules');

// Create wallet page programmatically if it doesn't exist
function create_wallet_page_if_not_exists() {
    if (!get_page_by_path('wallet')) {
        $wallet_page = array(
            'post_title'    => 'My Wallet',
            'post_content'  => '[wallet_page]', // We'll create this shortcode
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'wallet'
        );
        wp_insert_post($wallet_page);
    }
}
add_action('after_switch_theme', 'create_wallet_page_if_not_exists');

// Wallet shortcode as backup
function wallet_page_shortcode() {
    if (!is_user_logged_in()) {
        return '<p>Please <a href="' . wp_login_url(get_permalink()) . '">login</a> to view your wallet.</p>';
    }

    ob_start();
    include get_template_directory() . '/page-wallet.php';
    return ob_get_clean();
}
add_shortcode('wallet_page', 'wallet_page_shortcode');

function wallet_page_query_vars($vars) {
    $vars[] = 'wallet_page';
    return $vars;
}
add_filter('query_vars', 'wallet_page_query_vars');

function wallet_page_template($template) {
    if (get_query_var('wallet_page')) {
        $wallet_template = get_template_directory() . '/page-wallet.php';
        if (file_exists($wallet_template)) {
            return $wallet_template;
        }
    }
    return $template;
}
add_filter('template_include', 'wallet_page_template');

// Terms and Conditions page template
function terms_conditions_page_template($template) {
    global $post;

    if (is_page() && $post) {
        // Check if this is a terms and conditions page
        if (stripos($post->post_title, 'terms') !== false ||
            stripos($post->post_name, 'terms') !== false ||
            stripos($post->post_title, 'condition') !== false ||
            stripos($post->post_name, 'condition') !== false) {

            $terms_template = get_template_directory() . '/page-terms-conditions.php';
            if (file_exists($terms_template)) {
                return $terms_template;
            }
        }
    }
    return $template;
}
add_filter('template_include', 'terms_conditions_page_template');

// Privacy Policy page template
function privacy_policy_page_template($template) {
    global $post;

    if (is_page() && $post) {
        // Check if this is a privacy policy page
        if (stripos($post->post_title, 'privacy') !== false ||
            stripos($post->post_name, 'privacy') !== false ||
            stripos($post->post_title, 'policy') !== false ||
            stripos($post->post_name, 'policy') !== false) {

            $privacy_template = get_template_directory() . '/page-privacy-policy.php';
            if (file_exists($privacy_template)) {
                return $privacy_template;
            }
        }
    }
    return $template;
}
add_filter('template_include', 'privacy_policy_page_template');

// Create Privacy Policy page if it doesn't exist
function create_privacy_policy_page_if_not_exists() {
    // Check if page already exists
    $existing_page = get_page_by_path('privacy-policy');
    if (!$existing_page) {
        $existing_page = get_page_by_path('privacy');
    }

    if (!$existing_page) {
        $privacy_page = array(
            'post_title'    => 'Privacy Policy',
            'post_content'  => '
                <h2 id="information-we-collect">1. Information We Collect</h2>
                <p>We collect personal information that you voluntarily provide to us when you register on our website, participate in competitions, or otherwise interact with our services.</p>

                <h3 id="personal-information">1.1 Personal Information</h3>
                <p>This may include your name, email address, postal address, phone number, date of birth, and payment information.</p>

                <h3 id="newsletter-information">1.2 Newsletter Information</h3>
                <p>When you subscribe to our newsletter, we collect your email address, first and last name, subscription preferences, and signup date.</p>

                <h3 id="usage-information">1.3 Usage Information</h3>
                <p>We automatically collect certain information about your device and how you interact with our website, including IP address, browser type, pages visited, and time spent on the site.</p>

                <h2 id="newsletter-communications">2. Newsletter Communications</h2>
                <p>We use Mailchimp to manage our newsletter communications. When you subscribe to our newsletter:</p>
                <ul>
                    <li><strong>Legal basis:</strong> Your explicit consent</li>
                    <li><strong>Purpose:</strong> Competition updates, exclusive offers, and free entry opportunities</li>
                    <li><strong>Frequency:</strong> Typically 1-2 emails per week</li>
                    <li><strong>Your rights:</strong> You can unsubscribe at any time using the link in our emails</li>
                    <li><strong>Data retention:</strong> Until you unsubscribe or request deletion</li>
                </ul>

                <h2 id="how-we-use-information">3. How We Use Your Information</h2>
                <p>We use the information we collect for various purposes, including:</p>
                <ul>
                    <li>Processing competition entries and payments</li>
                    <li>Contacting winners and delivering prizes</li>
                    <li>Providing customer support</li>
                    <li>Sending promotional communications (with your consent)</li>
                    <li>Improving our website and services</li>
                    <li>Complying with legal obligations</li>
                </ul>
            ',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'privacy-policy'
        );

        $page_id = wp_insert_post($privacy_page);

        // Set as WooCommerce privacy page if WooCommerce is active
        if (class_exists('WooCommerce')) {
            update_option('woocommerce_privacy_policy_page_id', $page_id);
        }
    }
}
add_action('after_switch_theme', 'create_privacy_policy_page_if_not_exists');

// Include admin functionality
if (is_admin()) {
    require_once get_template_directory() . '/admin-cleanup.php';
    require_once get_template_directory() . '/admin-wallet-management.php';
}

// Create Terms and Conditions page if it doesn't exist
function create_terms_conditions_page_if_not_exists() {
    // Check if page already exists
    $existing_page = get_page_by_path('terms-and-conditions');
    if (!$existing_page) {
        $existing_page = get_page_by_path('terms-conditions');
    }
    if (!$existing_page) {
        $existing_page = get_page_by_path('terms');
    }

    if (!$existing_page) {
        $terms_page = array(
            'post_title'    => 'Terms and Conditions',
            'post_content'  => '
                <div id="acceptance" class="content-section">
                    <div class="content-card">
                        <div class="card-header">
                            <h2 class="section-title" style="color: #10b981;">
                                <i class="fas fa-check-circle"></i>
                                1. Acceptance of Terms
                            </h2>
                        </div>
                        <div class="card-content">
                            <p class="section-text">
                                By accessing, browsing, or using our website and services, you acknowledge that you have read,
                                understood, and agree to be bound by these Terms and Conditions and our Privacy Policy.
                            </p>
                            <ul class="terms-list">
                                <li><span class="list-bullet">•</span>These terms constitute a legally binding agreement between you and Film Collectables Competitions Limited</li>
                                <li><span class="list-bullet">•</span>Your continued use of our services constitutes acceptance of any updates to these terms</li>
                                <li><span class="list-bullet">•</span>If you disagree with any part of these terms, you must discontinue use immediately</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div id="definitions" class="content-section">
                    <div class="content-card">
                        <div class="card-header">
                            <h2 class="section-title" style="color: #06b6d4;">
                                <i class="fas fa-file-text"></i>
                                2. Definitions
                            </h2>
                        </div>
                        <div class="card-content">
                            <p class="section-text">
                                For the purposes of these Terms and Conditions, the following definitions apply:
                            </p>
                            <div class="definition-grid">
                                <div class="definition-item">
                                    <h4 class="definition-term">"Competition"</h4>
                                    <p class="definition-desc">Any prize draw, contest, or promotional activity offered through our platform</p>
                                </div>
                                <div class="definition-item">
                                    <h4 class="definition-term">"Entry"</h4>
                                    <p class="definition-desc">A purchased ticket or participation in a competition</p>
                                </div>
                                <div class="definition-item">
                                    <h4 class="definition-term">"User"</h4>
                                    <p class="definition-desc">Any person who accesses or uses our website or services</p>
                                </div>
                                <div class="definition-item">
                                    <h4 class="definition-term">"Prize"</h4>
                                    <p class="definition-desc">Any item, service, or monetary reward offered in competitions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="eligibility" class="content-section">
                    <div class="content-card">
                        <div class="card-header">
                            <h2 class="section-title" style="color: #3b82f6;">
                                <i class="fas fa-users"></i>
                                3. Eligibility
                            </h2>
                        </div>
                        <div class="card-content">
                            <p class="section-text">
                                Competitions are open to residents of the United Kingdom and Ireland only.
                            </p>
                            <ul class="terms-list">
                                <li><span class="list-bullet">•</span>You must be 18 years of age or older to participate</li>
                                <li><span class="list-bullet">•</span>Employees of Film Collectables Competitions Limited and their immediate families are excluded</li>
                                <li><span class="list-bullet">•</span>Only one account per person is permitted</li>
                                <li><span class="list-bullet">•</span>Valid identification may be required to claim prizes</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="contact-box">
                    <div class="contact-info">Company Information</div>
                    <div class="contact-details">
                        <strong>FILM COLLECTABLES COMPETITIONS LIMITED</strong><br>
                        Ground Floor, Gallery Building<br>
                        65-69 Dublin Rd, Belfast, BT2 7HG<br>
                        Company Registration: NI730025
                    </div>
                </div>
            ',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'terms-and-conditions'
        );

        $page_id = wp_insert_post($terms_page);

        // Set as WooCommerce terms page if WooCommerce is active
        if (class_exists('WooCommerce')) {
            update_option('woocommerce_terms_page_id', $page_id);
        }
    }
}
add_action('after_switch_theme', 'create_terms_conditions_page_if_not_exists');

// Fix WooCommerce admin table layout issue
function fix_woocommerce_admin_table_layout() {
    $screen = get_current_screen();
    if ($screen && ($screen->id === 'edit-product' || $screen->post_type === 'product')) {
        ?>
        <style>
        /* Force proper table layout for WooCommerce Products */
        body.post-type-product .wp-list-table,
        body.post-type-product .wp-list-table tbody,
        body.post-type-product .wp-list-table thead,
        body.post-type-product .wp-list-table tfoot {
            display: table !important;
            width: 100% !important;
        }

        body.post-type-product .wp-list-table tr {
            display: table-row !important;
        }

        body.post-type-product .wp-list-table th,
        body.post-type-product .wp-list-table td {
            display: table-cell !important;
            padding: 8px 10px !important;
            vertical-align: middle !important;
            border-bottom: 1px solid #c3c4c7 !important;
        }

        /* Hide mobile responsive labels */
        body.post-type-product .wp-list-table td:before {
            display: none !important;
            content: none !important;
        }

        /* Column widths */
        body.post-type-product .wp-list-table .column-cb { width: 2.2em !important; }
        body.post-type-product .wp-list-table .column-thumb { width: 52px !important; }
        body.post-type-product .wp-list-table .column-name { width: auto !important; }
        body.post-type-product .wp-list-table .column-sku { width: 12% !important; }
        body.post-type-product .wp-list-table .column-is_in_stock { width: 12% !important; }
        body.post-type-product .wp-list-table .column-price { width: 12% !important; }
        body.post-type-product .wp-list-table .column-product_cat { width: 15% !important; }
        body.post-type-product .wp-list-table .column-product_tag { width: 15% !important; }
        body.post-type-product .wp-list-table .column-featured { width: 8% !important; }
        body.post-type-product .wp-list-table .column-date { width: 10% !important; }

        /* Override responsive styles completely */
        @media screen and (max-width: 782px) {
            body.post-type-product .wp-list-table,
            body.post-type-product .wp-list-table tbody,
            body.post-type-product .wp-list-table tr,
            body.post-type-product .wp-list-table th,
            body.post-type-product .wp-list-table td {
                display: table-cell !important;
            }

            body.post-type-product .wp-list-table tr {
                display: table-row !important;
            }

            body.post-type-product .wp-list-table {
                display: table !important;
            }

            body.post-type-product .wp-list-table td {
                padding-left: 10px !important;
            }
        }
        </style>
        <?php
    }
}
add_action('admin_head', 'fix_woocommerce_admin_table_layout');

// Enqueue admin CSS fix
function enqueue_admin_table_fix() {
    wp_enqueue_style('admin-table-fix', get_template_directory_uri() . '/admin-fix.css', array(), '1.0.0');
}
add_action('admin_enqueue_scripts', 'enqueue_admin_table_fix');

/**
 * Wallet Integration with WooCommerce - Optional Application
 */
function add_wallet_discount_to_cart() {
    if (is_admin() && !defined('DOING_AJAX')) return;
    if (!is_user_logged_in()) return;

    // Only apply if user has chosen to use wallet credit
    if (!WC()->session->get('use_wallet_credit')) return;

    $user_id = get_current_user_id();
    $wallet_balance = get_user_wallet_balance($user_id);

    if ($wallet_balance <= 0) return;

    $cart_total = WC()->cart->get_subtotal();
    $discount_amount = min($wallet_balance, $cart_total);

    if ($discount_amount > 0) {
        WC()->cart->add_fee('Wallet Credit Applied', -$discount_amount);
    }
}
add_action('woocommerce_cart_calculate_fees', 'add_wallet_discount_to_cart');

function deduct_wallet_credit_after_order($order_id) {
    $order = wc_get_order($order_id);
    $user_id = $order->get_user_id();

    if (!$user_id) return;

    // Check if wallet credit was applied
    foreach ($order->get_fees() as $fee) {
        if ($fee->get_name() === 'Wallet Credit Applied') {
            $credit_used = abs($fee->get_total());
            if ($credit_used > 0) {
                deduct_from_user_wallet($user_id, $credit_used, 'Used for order #' . $order_id);
            }
            break;
        }
    }
}
add_action('woocommerce_order_status_completed', 'deduct_wallet_credit_after_order');
add_action('woocommerce_order_status_processing', 'deduct_wallet_credit_after_order');

/**
 * Add Instant Wins endpoint to My Account
 */
function add_instant_wins_endpoint() {
    add_rewrite_endpoint('instant-wins', EP_ROOT | EP_PAGES);
}
add_action('init', 'add_instant_wins_endpoint');

function add_instant_wins_query_vars($vars) {
    $vars[] = 'instant-wins';
    return $vars;
}
add_filter('woocommerce_get_query_vars', 'add_instant_wins_query_vars');

function add_instant_wins_menu_item($items) {
    // Add instant wins after orders but before edit-address
    $new_items = array();
    foreach ($items as $key => $item) {
        $new_items[$key] = $item;
        if ($key === 'orders') {
            $new_items['instant-wins'] = 'Instant Wins';
        }
    }
    return $new_items;
}
add_filter('woocommerce_account_menu_items', 'add_instant_wins_menu_item');

function instant_wins_endpoint_content() {
    include get_template_directory() . '/woocommerce/myaccount/instant-wins.php';
}
add_action('woocommerce_account_instant-wins_endpoint', 'instant_wins_endpoint_content');

// Flush rewrite rules on theme activation to ensure endpoints work
function flush_rewrite_rules_on_theme_activation() {
    add_instant_wins_endpoint();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_rewrite_rules_on_theme_activation');

/**
 * Get competition status without requiring Competition_Manager class
 * This is a fallback function for theme templates
 */
function get_competition_status_simple($product_id) {
    $is_competition = get_post_meta($product_id, '_is_competition', true);
    if ($is_competition !== 'yes') {
        return 'not_competition';
    }

    // Check if winner has been selected
    $winner_selected = get_post_meta($product_id, 'winner_user_id', true);
    if ($winner_selected) {
        return 'completed';
    }

    $current_time = current_time('timestamp');

    // Check start date
    $start_date = get_post_meta($product_id, '_competition_start_date', true);
    if ($start_date && strtotime($start_date) > $current_time) {
        return 'scheduled';
    }

    // Check if competition is sold out
    $max_tickets = get_post_meta($product_id, '_competition_max_tickets', true);
    $sold_tickets = get_post_meta($product_id, '_competition_sold_tickets', true) ?: 0;

    if ($max_tickets && $sold_tickets >= $max_tickets) {
        return 'awaiting_draw'; // Competition is sold out
    }

    // Check end date
    $end_date = get_post_meta($product_id, '_competition_end_date', true);
    if ($end_date && strtotime($end_date) < $current_time) {
        return 'awaiting_draw';
    }

    return 'active';
}

/**
 * Prevent caching for AJAX responses
 */
function prevent_ajax_caching() {
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
}

/**
 * Test AJAX handler to verify AJAX is working
 */
function test_ajax_handler() {
    prevent_ajax_caching();

    wp_send_json_success(array(
        'message' => 'AJAX is working!',
        'timestamp' => current_time('mysql')
    ));
}
add_action('wp_ajax_test_ajax', 'test_ajax_handler');
add_action('wp_ajax_nopriv_test_ajax', 'test_ajax_handler');

/**
 * Simple competition data handler for refreshing competition cards
 */
function simple_get_competition_data() {
    prevent_ajax_caching();

    error_log('Simple competition data handler called');

    $competition_id = isset($_POST['competition_id']) ? intval($_POST['competition_id']) : 0;

    if (!$competition_id) {
        wp_send_json_error('No competition ID provided');
    }

    // Get competition data
    $product = wc_get_product($competition_id);
    if (!$product) {
        wp_send_json_error('Competition not found');
    }

    $max_tickets = get_post_meta($competition_id, '_competition_max_tickets', true);
    $sold_tickets = get_post_meta($competition_id, '_competition_sold_tickets', true) ?: 0;
    $end_date = get_post_meta($competition_id, '_competition_end_date', true);
    $status = get_competition_status_simple($competition_id);
    $price = $product->get_price();

    // Calculate progress and remaining tickets
    $progress_percentage = $max_tickets > 0 ? min(100, ($sold_tickets / $max_tickets) * 100) : 0;
    $remaining_tickets = max(0, $max_tickets - $sold_tickets);

    // Calculate time left
    $time_left = '';
    if ($end_date) {
        $current_time = current_time('timestamp');
        $end_timestamp = strtotime($end_date);
        if ($end_timestamp > $current_time) {
            $time_left = human_time_diff($current_time, $end_timestamp);
        } else {
            $time_left = 'Ended';
        }
    }

    wp_send_json_success(array(
        'id' => $competition_id,
        'title' => get_the_title($competition_id),
        'price' => floatval($price),
        'max_tickets' => intval($max_tickets),
        'sold_tickets' => intval($sold_tickets),
        'remaining_tickets' => $remaining_tickets,
        'progress_percentage' => $progress_percentage,
        'status' => $status,
        'end_date' => $end_date,
        'time_left' => $time_left,
        'is_sold_out' => $status === 'awaiting_draw'
    ));
}
add_action('wp_ajax_simple_get_competition_data', 'simple_get_competition_data');
add_action('wp_ajax_nopriv_simple_get_competition_data', 'simple_get_competition_data');

/**
 * Get cart count AJAX handler
 */
function get_cart_count_handler() {
    prevent_ajax_caching();

    if (class_exists('WooCommerce')) {
        $count = WC()->cart->get_cart_contents_count();
        $total = WC()->cart->get_cart_subtotal();
        wp_send_json_success(array(
            'count' => $count,
            'total' => $total
        ));
    } else {
        wp_send_json_error('WooCommerce not available');
    }
}
add_action('wp_ajax_get_cart_count', 'get_cart_count_handler');
add_action('wp_ajax_nopriv_get_cart_count', 'get_cart_count_handler');

/**
 * Check instant win AJAX handler
 */
function check_instant_win_handler() {
    // For now, just return no instant win
    wp_send_json_success(array('is_winner' => false));
}
add_action('wp_ajax_check_instant_win', 'check_instant_win_handler');
add_action('wp_ajax_nopriv_check_instant_win', 'check_instant_win_handler');

/**
 * Toggle wallet credit AJAX handler
 */
function toggle_wallet_credit_handler() {
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
    }

    $use_wallet_credit = isset($_POST['use_wallet_credit']) ? intval($_POST['use_wallet_credit']) : 0;

    // Store the preference in WooCommerce session
    if (class_exists('WooCommerce') && WC()->session) {
        WC()->session->set('use_wallet_credit', $use_wallet_credit);

        // Clear cart totals to force recalculation
        WC()->cart->calculate_totals();

        wp_send_json_success(array(
            'message' => $use_wallet_credit ? 'Wallet credit enabled' : 'Wallet credit disabled',
            'use_wallet_credit' => $use_wallet_credit
        ));
    } else {
        wp_send_json_error('WooCommerce session not available');
    }
}
add_action('wp_ajax_toggle_wallet_credit', 'toggle_wallet_credit_handler');
add_action('wp_ajax_nopriv_toggle_wallet_credit', 'toggle_wallet_credit_handler');

/**
 * Footer newsletter signup AJAX handler
 */
function footer_newsletter_signup_handler() {
    error_log('NEWSLETTER SIGNUP: Handler called');
    error_log('NEWSLETTER SIGNUP: POST data: ' . print_r($_POST, true));

    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'footer_newsletter')) {
        error_log('NEWSLETTER SIGNUP: Invalid nonce');
        wp_send_json_error(array('message' => 'Security check failed.'));
    }

    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    error_log('NEWSLETTER SIGNUP: Email: ' . $email);

    if (!$email || !is_email($email)) {
        error_log('NEWSLETTER SIGNUP: Invalid email');
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
    }

    try {
        // Use the existing newsletter signup action
        error_log('NEWSLETTER SIGNUP: Calling newsletter signup action');
        do_action('filmcollectables_newsletter_signup', $email, '', '', null);

        error_log('NEWSLETTER SIGNUP: Success');
        wp_send_json_success(array('message' => 'Successfully subscribed to our mailing list!'));
    } catch (Exception $e) {
        error_log('NEWSLETTER SIGNUP: Exception: ' . $e->getMessage());
        wp_send_json_error(array('message' => 'Subscription failed: ' . $e->getMessage()));
    }
}
add_action('wp_ajax_footer_newsletter_signup', 'footer_newsletter_signup_handler');
add_action('wp_ajax_nopriv_footer_newsletter_signup', 'footer_newsletter_signup_handler');

/**
 * AJAX endpoint to check if tickets have been generated for an order
 */
function check_order_tickets_ajax() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'check_order_tickets')) {
        wp_die('Security check failed');
    }

    $order_id = intval($_POST['order_id']);
    if (!$order_id) {
        wp_send_json_error('Invalid order ID');
    }

    // Get tickets for this order
    global $wpdb;
    $tickets_table = $wpdb->prefix . 'competition_tickets';

    $tickets = $wpdb->get_results($wpdb->prepare(
        "SELECT ct.*, p.post_title as competition_title
         FROM $tickets_table ct
         LEFT JOIN {$wpdb->posts} p ON ct.competition_id = p.ID
         WHERE ct.order_id = %d
         ORDER BY ct.competition_id, ct.ticket_number",
        $order_id
    ));

    $response = array(
        'has_tickets' => !empty($tickets),
        'ticket_count' => count($tickets),
        'tickets' => array()
    );

    // Group tickets by competition
    if (!empty($tickets)) {
        $tickets_by_competition = array();
        foreach ($tickets as $ticket) {
            $tickets_by_competition[$ticket->competition_id][] = $ticket;
        }
        $response['tickets'] = $tickets_by_competition;
    }

    wp_send_json_success($response);
}

// Register AJAX endpoints for ticket checking
add_action('wp_ajax_check_order_tickets', 'check_order_tickets_ajax');
add_action('wp_ajax_nopriv_check_order_tickets', 'check_order_tickets_ajax');

/**
 * Add debug information to order notes for Cashflows payments
 */
function filmcollectables_debug_cashflows_order($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    $payment_method = $order->get_payment_method();
    $is_cashflows = strpos($payment_method, 'iccf_') === 0;

    if ($is_cashflows) {
        $order->add_order_note('DEBUG: Cashflows payment detected - ticket generation should be triggered');

        // Check if tickets exist
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $ticket_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $tickets_table WHERE order_id = %d",
            $order_id
        ));

        $order->add_order_note("DEBUG: Found $ticket_count tickets for this order");
    }
}
add_action('woocommerce_payment_complete', 'filmcollectables_debug_cashflows_order', 20);

/**
 * Force ticket generation for Cashflows orders that might have missed it
 */
function filmcollectables_ensure_tickets_generated($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    $payment_method = $order->get_payment_method();
    $is_cashflows = strpos($payment_method, 'iccf_') === 0;

    if ($is_cashflows && in_array($order->get_status(), ['processing', 'completed'])) {
        // Check if tickets exist
        global $wpdb;
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $ticket_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $tickets_table WHERE order_id = %d",
            $order_id
        ));

        if ($ticket_count == 0) {
            // No tickets found, check if order has competition items
            $has_competition_items = false;
            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();
                $is_competition = get_post_meta($product_id, '_is_competition', true);
                if ($is_competition === 'yes') {
                    $has_competition_items = true;
                    break;
                }
            }

            if ($has_competition_items) {
                error_log("FORCE TICKET GENERATION: Order #$order_id has competition items but no tickets");
                $order->add_order_note('DEBUG: Force triggering ticket generation for Cashflows order');

                // Trigger ticket generation manually
                if (class_exists('CompetitionManager')) {
                    $cm = new CompetitionManager();
                    $cm->generate_tickets_on_payment_complete($order_id);
                } elseif (class_exists('Competition_Manager')) {
                    $cm = new Competition_Manager();
                    $cm->generate_tickets_on_payment_complete($order_id);
                }
            }
        }
    }
}
add_action('woocommerce_order_status_processing', 'filmcollectables_ensure_tickets_generated', 25);

/**
 * Generate tickets immediately for Cashflows orders when they're created
 * This prevents the timing issue where users see the thank you page before tickets are generated
 */
function filmcollectables_generate_tickets_on_cashflows_order($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) {
        return;
    }

    $payment_method = $order->get_payment_method();
    $is_cashflows = strpos($payment_method, 'cashflows') !== false;

    if ($is_cashflows) {
        // Check if order has competition items
        $has_competition_items = false;
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $is_competition = get_post_meta($product_id, '_is_competition', true);
            if ($is_competition === 'yes') {
                $has_competition_items = true;
                break;
            }
        }

        if ($has_competition_items) {
            error_log("CASHFLOWS IMMEDIATE: Generating tickets for order #$order_id immediately");

            // Force ticket generation by calling the private method directly
            global $wpdb;
            $tickets_table = $wpdb->prefix . 'competition_tickets';

            // Check if tickets already exist
            $existing_tickets = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $tickets_table WHERE order_id = %d",
                $order_id
            ));

            if ($existing_tickets == 0) {
                error_log("CASHFLOWS IMMEDIATE: No existing tickets found, generating now...");

                // Get the Competition Manager instance and force ticket generation
                if (class_exists('CompetitionManager')) {
                    $cm = new CompetitionManager();
                    // Use reflection to call the private method directly
                    $reflection = new ReflectionClass($cm);
                    $method = $reflection->getMethod('generate_tickets_for_order');
                    $method->setAccessible(true);
                    $method->invoke($cm, $order_id);
                    error_log("CASHFLOWS IMMEDIATE: Called generate_tickets_for_order directly");
                }
            } else {
                error_log("CASHFLOWS IMMEDIATE: Found $existing_tickets existing tickets, skipping generation");
            }
        }
    }
}

// Hook into order creation for Cashflows payments
add_action('woocommerce_checkout_order_processed', 'filmcollectables_generate_tickets_on_cashflows_order', 20);
add_action('woocommerce_thankyou', 'filmcollectables_generate_tickets_on_cashflows_order', 5);

/**
 * Email Verification System
 */

/**
 * Generate email verification token
 */
function generate_email_verification_token($user_id) {
    $token = wp_generate_password(32, false);
    $expiration = time() + (24 * 60 * 60); // 24 hours
    
    // Store token and expiration in user meta
    update_user_meta($user_id, 'email_verification_token', $token);
    update_user_meta($user_id, 'email_verification_token_expiration', $expiration);
    update_user_meta($user_id, 'email_verified', 'no');
    
    return $token;
}

/**
 * Send email verification email - Simple version with plugin override support
 */
function send_email_verification_email($user_id, $token) {
    // Check if a plugin wants to override this function
    if (has_filter('send_verification_email_method')) {
        $result = apply_filters('send_verification_email_method', false, $user_id, $token);
        if ($result !== false) {
            return $result;
        }
    }
    
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return false;
    }
    
    $verification_url = home_url('/verify-email/?token=' . $token . '&user_id=' . $user_id);
    $site_name = get_bloginfo('name');
    
    $subject = 'Verify your email address - ' . $site_name;
    
    $message = "Hi " . ($user->first_name ? $user->first_name : $user->display_name) . ",\n\n";
    $message .= "Thank you for registering with " . $site_name . "!\n\n";
    $message .= "To complete your registration and start entering competitions, please verify your email address by clicking the link below:\n\n";
    $message .= $verification_url . "\n\n";
    $message .= "This link will expire in 24 hours.\n\n";
    $message .= "If you did not create this account, please ignore this email.\n\n";
    $message .= "Thank you!\n";
    $message .= $site_name;
    
    $headers = array('Content-Type: text/plain; charset=UTF-8');
    
    return wp_mail($user->user_email, $subject, $message, $headers);
}

/**
 * Verify email token
 */
function verify_email_token($user_id, $token) {
    $stored_token = get_user_meta($user_id, 'email_verification_token', true);
    $expiration = get_user_meta($user_id, 'email_verification_token_expiration', true);
    
    if (empty($stored_token) || empty($expiration)) {
        return false;
    }
    
    if (time() > $expiration) {
        return false; // Token expired
    }
    
    if ($stored_token !== $token) {
        return false; // Invalid token
    }
    
    return true;
}

/**
 * Mark user email as verified
 */
function mark_email_as_verified($user_id) {
    $result = update_user_meta($user_id, 'email_verified', 'yes');
    delete_user_meta($user_id, 'email_verification_token');
    delete_user_meta($user_id, 'email_verification_token_expiration');

    // Debug logging
    error_log('mark_email_as_verified called for user ' . $user_id . ', result: ' . ($result ? 'success' : 'failed'));

    // Clear user cache to ensure fresh data
    wp_cache_delete($user_id, 'user_meta');
    clean_user_cache($user_id);
}

/**
 * Check if user email is verified
 */
function is_user_email_verified($user_id) {
    // Clear cache to get fresh data
    wp_cache_delete($user_id, 'user_meta');

    $verified = get_user_meta($user_id, 'email_verified', true);

    // Debug logging
    error_log('is_user_email_verified called for user ' . $user_id . ', verified status: ' . ($verified ? $verified : 'empty'));

    return $verified === 'yes';
}

/**
 * Resend email verification email
 */
function resend_email_verification($user_id) {
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return false;
    }
    
    // Check if user is already verified
    if (is_user_email_verified($user_id)) {
        return false;
    }
    
    // Generate new token
    $token = generate_email_verification_token($user_id);
    
    // Send verification email
    return send_email_verification_email($user_id, $token);
}

/**
 * Handle resend verification email request
 */
function handle_resend_verification() {
    if (!isset($_POST['resend_verification_nonce']) || !wp_verify_nonce($_POST['resend_verification_nonce'], 'resend_verification')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }
    
    if (!isset($_POST['user_email']) || !is_email($_POST['user_email'])) {
        wp_send_json_error(array('message' => 'Please provide a valid email address'));
        return;
    }
    
    $email = sanitize_email($_POST['user_email']);
    $user = get_user_by('email', $email);
    
    if (!$user) {
        wp_send_json_error(array('message' => 'No account found with that email address'));
        return;
    }
    
    if (is_user_email_verified($user->ID)) {
        wp_send_json_error(array('message' => 'Email is already verified'));
        return;
    }
    
    if (resend_email_verification($user->ID)) {
        wp_send_json_success(array('message' => 'Verification email sent successfully'));
    } else {
        wp_send_json_error(array('message' => 'Failed to send verification email'));
    }
}
add_action('wp_ajax_resend_verification', 'handle_resend_verification');
add_action('wp_ajax_nopriv_resend_verification', 'handle_resend_verification');

/**
 * Handle email verification
 */
function handle_email_verification() {
    if (!isset($_GET['token']) || !isset($_GET['user_id'])) {
        wp_die('Invalid verification link.');
    }
    
    $token = sanitize_text_field($_GET['token']);
    $user_id = intval($_GET['user_id']);
    
    if (!verify_email_token($user_id, $token)) {
        wp_die('Invalid or expired verification link. Please request a new verification email.');
    }
    
    mark_email_as_verified($user_id);
    
    // Auto login the user after successful verification
    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id);
    
    wp_redirect(home_url('/my-account/?verified=1'));
    exit;
}
/**
 * Create verify-email page if it doesn't exist
 */
function create_verify_email_page() {
    $page = get_page_by_path('verify-email');
    if (!$page) {
        $page_data = array(
            'post_title'     => 'Email Verification',
            'post_content'   => '[email_verification]',
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'post_name'      => 'verify-email',
            'page_template'  => 'page-verify-email.php'
        );

        $page_id = wp_insert_post($page_data);
        if ($page_id) {
            update_post_meta($page_id, '_wp_page_template', 'page-verify-email.php');
            error_log('Created verify-email page with ID: ' . $page_id);
        }
    }
}
add_action('init', 'create_verify_email_page');

/**
 * Global email verification handler - catches verification attempts anywhere
 */
add_action('init', function() {
    if (isset($_GET['token']) && isset($_GET['user_id']) && !is_admin()) {
        $token = sanitize_text_field($_GET['token']);
        $user_id = intval($_GET['user_id']);

        error_log('Global email verification attempt - User ID: ' . $user_id . ', Token: ' . substr($token, 0, 8) . '...');

        if (verify_email_token($user_id, $token)) {
            mark_email_as_verified($user_id);

            // Auto login the user after successful verification
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);

            error_log('Global email verification successful for user ID: ' . $user_id);

            wp_redirect(home_url('/my-account/?verified=1'));
            exit;
        } else {
            error_log('Global email verification failed for user ID: ' . $user_id);
            // Don't redirect on failure, let the page handle it
        }
    }
});

/**
 * Handle email verification via URL parameters
 * This serves as a fallback if the page template doesn't work
 */
add_action('template_redirect', function() {
    if (is_page('verify-email') && isset($_GET['token']) && isset($_GET['user_id'])) {
        $token = sanitize_text_field($_GET['token']);
        $user_id = intval($_GET['user_id']);

        error_log('Email verification attempt via template_redirect - User ID: ' . $user_id);

        if (verify_email_token($user_id, $token)) {
            mark_email_as_verified($user_id);

            // Auto login the user after successful verification
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);

            // Clear any caches
            wp_cache_delete($user_id, 'user_meta');
            clean_user_cache($user_id);

            error_log('Email verification successful for user ID: ' . $user_id);

            wp_redirect(home_url('/my-account/?verified=1'));
            exit;
        } else {
            error_log('Email verification failed for user ID: ' . $user_id);
            wp_redirect(home_url('/verify-email/?error=1'));
            exit;
        }
    }
});

/**
 * Email verification shortcode
 */
function email_verification_shortcode($atts) {
    ob_start();

    if (isset($_GET['token']) && isset($_GET['user_id'])) {
        $token = sanitize_text_field($_GET['token']);
        $user_id = intval($_GET['user_id']);

        if (verify_email_token($user_id, $token)) {
            mark_email_as_verified($user_id);

            // Auto login the user after successful verification
            wp_set_current_user($user_id);
            wp_set_auth_cookie($user_id);

            // Clear any caches
            wp_cache_delete($user_id, 'user_meta');
            clean_user_cache($user_id);

            ?>
            <div class="verification-success" style="text-align: center; padding: 2rem; background: #d1fae5; border: 1px solid #10b981; border-radius: 8px; margin: 2rem 0;">
                <h2 style="color: #065f46; margin-bottom: 1rem;">✅ Email Verified Successfully!</h2>
                <p style="color: #047857; margin-bottom: 1.5rem;">Your email has been verified and you can now enter competitions.</p>
                <p style="color: #047857; margin-bottom: 2rem;">You have been logged in automatically.</p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="<?php echo home_url('/my-account/'); ?>" class="btn btn-primary" style="background: #0ea5e9; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">Go to My Account</a>
                    <a href="<?php echo home_url('/competitions/'); ?>" class="btn btn-secondary" style="background: #6b7280; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">Browse Competitions</a>
                </div>
            </div>
            <script>
                // Redirect to account page after 3 seconds
                setTimeout(function() {
                    window.location.href = '<?php echo home_url('/my-account/?verified=1'); ?>';
                }, 3000);
            </script>
            <?php
        } else {
            ?>
            <div class="verification-error" style="text-align: center; padding: 2rem; background: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; margin: 2rem 0;">
                <h2 style="color: #dc2626; margin-bottom: 1rem;">❌ Verification Failed</h2>
                <p style="color: #dc2626; margin-bottom: 1rem;">The verification link is invalid or has expired.</p>
                <p style="color: #dc2626; margin-bottom: 2rem;">Please request a new verification email.</p>
                <a href="<?php echo home_url('/login/'); ?>" class="btn btn-primary" style="background: #0ea5e9; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">Back to Login</a>
            </div>
            <?php
        }
    } elseif (isset($_GET['error'])) {
        ?>
        <div class="verification-error" style="text-align: center; padding: 2rem; background: #fef2f2; border: 1px solid #ef4444; border-radius: 8px; margin: 2rem 0;">
            <h2 style="color: #dc2626; margin-bottom: 1rem;">❌ Verification Failed</h2>
            <p style="color: #dc2626; margin-bottom: 1rem;">The verification link is invalid or has expired.</p>
            <p style="color: #dc2626; margin-bottom: 2rem;">Please request a new verification email.</p>
            <a href="<?php echo home_url('/login/'); ?>" class="btn btn-primary" style="background: #0ea5e9; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">Back to Login</a>
        </div>
        <?php
    } else {
        ?>
        <div class="verification-info" style="text-align: center; padding: 2rem; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; margin: 2rem 0;">
            <h2 style="color: #0369a1; margin-bottom: 1rem;">📧 Email Verification</h2>
            <p style="color: #0369a1; margin-bottom: 1rem;">Please check your email for the verification link.</p>
            <p style="color: #0369a1; margin-bottom: 2rem;">If you didn't receive the email, please check your spam folder.</p>
            <a href="<?php echo home_url('/login/'); ?>" class="btn btn-primary" style="background: #0ea5e9; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px;">Back to Login</a>
        </div>
        <?php
    }

    return ob_get_clean();
}
add_shortcode('email_verification', 'email_verification_shortcode');

function handle_custom_register() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['register_nonce'], 'custom_register_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }

    $first_name = sanitize_text_field($_POST['first_name']);
    $last_name = sanitize_text_field($_POST['last_name']);
    $date_of_birth = sanitize_text_field($_POST['date_of_birth']);
    $phone_number = sanitize_text_field($_POST['phone_number']);
    $email = sanitize_email($_POST['user_email']);
    $password = $_POST['user_password'];
    $newsletter_signup = isset($_POST['newsletter_signup']) ? true : false;

    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
        wp_send_json_error(array('message' => 'All required fields must be filled'));
        return;
    }

    if (!is_email($email)) {
        wp_send_json_error(array('message' => 'Please enter a valid email address'));
        return;
    }

    if (email_exists($email)) {
        wp_send_json_error(array('message' => 'An account with this email already exists'));
        return;
    }

    // Check for unique phone number
    $existing_phone = get_users(array(
        'meta_key' => 'phone_number',
        'meta_value' => $phone_number,
        'number' => 1
    ));

    if (!empty($existing_phone)) {
        wp_send_json_error(array('message' => 'An account with this phone number already exists'));
        return;
    }

    // Create username from email
    $username = sanitize_user(current(explode('@', $email)));
    if (username_exists($username)) {
        $username = $username . '_' . wp_rand(1000, 9999);
    }

    // Create user
    $user_id = wp_create_user($username, $password, $email);

    if (is_wp_error($user_id)) {
        wp_send_json_error(array('message' => 'Failed to create account. Please try again.'));
        return;
    }

    // Update user meta
    wp_update_user(array(
        'ID' => $user_id,
        'first_name' => $first_name,
        'last_name' => $last_name,
        'display_name' => $first_name . ' ' . $last_name
    ));

    // Add custom meta
    update_user_meta($user_id, 'date_of_birth', $date_of_birth);
    update_user_meta($user_id, 'phone_number', $phone_number);

    // Generate verification token and send verification email
    $token = generate_email_verification_token($user_id);
    $email_sent = send_email_verification_email($user_id, $token);

    if (!$email_sent) {
        error_log('Email verification email failed to send for user ' . $user_id);
        wp_send_json_error(array('message' => 'Account created but verification email failed to send. Please contact support.'));
        return;
    }

    // Log email status for debugging
    if (current_user_can('administrator')) {
        error_log('Email verification email sent for user ' . $user_id . ': SUCCESS');
    }

    // Handle newsletter signup
    if ($newsletter_signup) {
        // Store newsletter preference
        update_user_meta($user_id, 'newsletter_subscribed', 'yes');
        update_user_meta($user_id, 'newsletter_signup_date', current_time('mysql'));

        // Add to mailing list (this will be handled by the chosen email marketing plugin)
        do_action('filmcollectables_newsletter_signup', $email, $first_name, $last_name, $user_id);
    } else {
        update_user_meta($user_id, 'newsletter_subscribed', 'no');
    }

    // Auto login the user after successful registration
    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id);

    wp_send_json_success(array(
        'message' => 'Account created successfully! You have been logged in. Please check your email and click the verification link to enter competitions.',
        'redirect_url' => home_url('/my-account/')
    ));
}
add_action('wp_ajax_nopriv_custom_register', 'handle_custom_register');

/**
 * Add newsletter signup checkbox to checkout
 */
function add_newsletter_signup_to_checkout() {
    // Get the checkout object properly
    $checkout = WC()->checkout();
    if (!$checkout) {
        return;
    }

    // Only show for non-logged in users or users who haven't subscribed
    $show_checkbox = true;
    if (is_user_logged_in()) {
        $user_id = get_current_user_id();
        $user = wp_get_current_user();

        // Check both local subscription status and MC4WP status
        $already_subscribed = get_user_meta($user_id, 'newsletter_subscribed', true);
        $mc4wp_subscribed = function_exists('is_user_subscribed_mc4wp') ? is_user_subscribed_mc4wp($user->user_email) : false;

        if ($already_subscribed === 'yes' || $mc4wp_subscribed) {
            $show_checkbox = false;
        }
    }

    if ($show_checkbox) {
        echo '<div class="newsletter-signup-checkout" style="margin-top: 2rem;">';
        echo '<div class="form-row validate-optional">';
        echo '<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">';
        echo '<input type="checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" name="newsletter_signup" id="newsletter_signup" value="1" ' . checked($checkout->get_value('newsletter_signup'), 1, false) . ' />';
        echo '<span class="newsletter-checkbox-text">I consent to receive emails about competitions, exclusive offers, and free entry opportunities (optional)</span>';
        echo '</label>';
        echo '</div>';
        echo '</div>';
    }
}
// Move to be after customer details (billing address section)
add_action('woocommerce_checkout_after_customer_details', 'add_newsletter_signup_to_checkout');

/**
 * Prevent unverified users from accessing checkout page
 */
function restrict_checkout_page_for_unverified_users() {
    // Only apply on checkout page
    if (!is_checkout() || is_admin()) {
        return;
    }

    // Only apply to logged-in users (guests can't verify email anyway)
    if (!is_user_logged_in()) {
        return;
    }

    // Check if user's email is verified
    $user_id = get_current_user_id();
    $is_verified = is_user_email_verified($user_id);

    // Debug logging
    error_log("CHECKOUT RESTRICTION: User ID: $user_id, Verified: " . ($is_verified ? 'yes' : 'no'));

    if (!$is_verified) {
        // Check if cart contains competition products
        $has_competition_products = false;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $is_competition = get_post_meta($product_id, '_is_competition', true);
            error_log("CHECKOUT RESTRICTION: Product ID: " . $product_id . ", Is Competition: " . $is_competition);

            if ($product && $is_competition === 'yes') {
                $has_competition_products = true;
                break;
            }
        }

        error_log("CHECKOUT RESTRICTION: Has competition products: " . ($has_competition_products ? 'yes' : 'no'));

        if ($has_competition_products) {
            error_log("CHECKOUT RESTRICTION: Blocking unverified user from checkout");
            // Redirect to my account with message
            wc_add_notice('You must verify your email address before purchasing competition tickets. Please check your email for the verification link.', 'error');
            wp_redirect(home_url('/my-account/'));
            exit;
        }
    }
}
add_action('template_redirect', 'restrict_checkout_page_for_unverified_users');

/**
 * Prevent unverified users from accessing checkout form
 */
function restrict_checkout_for_unverified_users() {
    // Only apply to logged-in users (guests can't verify email anyway)
    if (!is_user_logged_in()) {
        return;
    }

    // Check if user's email is verified
    $user_id = get_current_user_id();
    if (!is_user_email_verified($user_id)) {
        // Check if cart contains competition products
        $has_competition_products = false;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $is_competition = get_post_meta($product_id, '_is_competition', true);

            if ($product && $is_competition === 'yes') {
                $has_competition_products = true;
                break;
            }
        }

        if ($has_competition_products) {
            // This will be handled by the checkout template
            return;
        }
    }
}
add_action('woocommerce_before_checkout_form', 'restrict_checkout_for_unverified_users');

/**
 * Validate checkout for unverified users
 */
function validate_checkout_for_unverified_users($data, $errors) {
    // Only apply to logged-in users
    if (!is_user_logged_in()) {
        return;
    }

    // Check if user's email is verified
    $user_id = get_current_user_id();
    if (!is_user_email_verified($user_id)) {
        // Check if cart contains competition products
        $has_competition_products = false;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $is_competition = get_post_meta($product_id, '_is_competition', true);

            if ($product && $is_competition === 'yes') {
                $has_competition_products = true;
                break;
            }
        }

        if ($has_competition_products) {
            $errors->add('email_verification', 'You must verify your email address before purchasing competition tickets. Please check your email for the verification link.');
        }
    }
}
add_action('woocommerce_after_checkout_validation', 'validate_checkout_for_unverified_users', 10, 2);

/**
 * Block order creation for unverified users
 */
function block_order_creation_for_unverified_users($order_id) {
    // Only apply to logged-in users
    if (!is_user_logged_in()) {
        return;
    }

    $order = wc_get_order($order_id);
    $user_id = $order->get_user_id();

    if ($user_id && !is_user_email_verified($user_id)) {
        // Check if order contains competition products
        $has_competition_products = false;
        foreach ($order->get_items() as $item) {
            $product = $item->get_product();
            if ($product) {
                $product_id = $product->get_id();
                $is_competition = get_post_meta($product_id, '_is_competition', true);

                if ($is_competition === 'yes') {
                    $has_competition_products = true;
                    break;
                }
            }
        }

        if ($has_competition_products) {
            // Cancel the order
            $order->update_status('cancelled', 'Order cancelled: User email not verified');

            // Add error notice
            wc_add_notice('Order cancelled: You must verify your email address before purchasing competition tickets.', 'error');

            // Redirect back to cart
            wp_redirect(wc_get_cart_url());
            exit;
        }
    }
}
add_action('woocommerce_checkout_order_processed', 'block_order_creation_for_unverified_users', 5);

/**
 * Show email verification notice in cart for unverified users
 */
function show_cart_email_verification_notice() {
    // Only apply to logged-in users
    if (!is_user_logged_in()) {
        return;
    }

    // Check if user's email is verified
    $user_id = get_current_user_id();
    if (!is_user_email_verified($user_id)) {
        // Check if cart contains competition products
        $has_competition_products = false;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $is_competition = get_post_meta($product_id, '_is_competition', true);

            if ($product && $is_competition === 'yes') {
                $has_competition_products = true;
                break;
            }
        }

        if ($has_competition_products) {
            wc_print_notice('⚠️ You must verify your email address before purchasing competition tickets. <a href="' . home_url('/my-account/') . '">Verify your email</a> or <a href="#" onclick="resendVerificationEmail(); return false;">resend verification email</a>.', 'notice');

            // Add JavaScript for resend functionality
            ?>
            <script>
            function resendVerificationEmail() {
                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=resend_verification'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Verification email sent! Please check your inbox.');
                    } else {
                        alert('Failed to send verification email. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
            }
            </script>
            <?php
        }
    }
}
add_action('woocommerce_before_cart', 'show_cart_email_verification_notice');

/**
 * Process newsletter signup during checkout
 */
function process_checkout_newsletter_signup($order_id) {
    if (isset($_POST['newsletter_signup']) && $_POST['newsletter_signup']) {
        $order = wc_get_order($order_id);
        $email = $order->get_billing_email();
        $first_name = $order->get_billing_first_name();
        $last_name = $order->get_billing_last_name();
        $user_id = $order->get_user_id();

        // Store newsletter preference
        if ($user_id) {
            update_user_meta($user_id, 'newsletter_subscribed', 'yes');
            update_user_meta($user_id, 'newsletter_signup_date', current_time('mysql'));
        }

        // Add order meta for tracking
        $order->update_meta_data('newsletter_signup', 'yes');
        $order->save();

        // Add to mailing list
        do_action('filmcollectables_newsletter_signup', $email, $first_name, $last_name, $user_id);
    }
}
add_action('woocommerce_checkout_order_processed', 'process_checkout_newsletter_signup');

/**
 * Newsletter management functions
 */

// Get all newsletter subscribers
function get_newsletter_subscribers() {
    global $wpdb;

    $subscribers = $wpdb->get_results("
        SELECT u.ID, u.user_email, u.display_name,
               um1.meta_value as first_name,
               um2.meta_value as last_name,
               um3.meta_value as signup_date
        FROM {$wpdb->users} u
        LEFT JOIN {$wpdb->usermeta} um1 ON (u.ID = um1.user_id AND um1.meta_key = 'first_name')
        LEFT JOIN {$wpdb->usermeta} um2 ON (u.ID = um2.user_id AND um2.meta_key = 'last_name')
        LEFT JOIN {$wpdb->usermeta} um3 ON (u.ID = um3.user_id AND um3.meta_key = 'newsletter_signup_date')
        WHERE EXISTS (
            SELECT 1 FROM {$wpdb->usermeta} um
            WHERE um.user_id = u.ID
            AND um.meta_key = 'newsletter_subscribed'
            AND um.meta_value = 'yes'
        )
        ORDER BY um3.meta_value DESC
    ");

    return $subscribers;
}

// Export newsletter subscribers to CSV
function export_newsletter_subscribers_csv() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $subscribers = get_newsletter_subscribers();

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="newsletter-subscribers-' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');
    fputcsv($output, array('Email', 'First Name', 'Last Name', 'Signup Date'));

    foreach ($subscribers as $subscriber) {
        fputcsv($output, array(
            $subscriber->user_email,
            $subscriber->first_name,
            $subscriber->last_name,
            $subscriber->signup_date
        ));
    }

    fclose($output);
    exit;
}

// Add admin menu for newsletter management
function add_newsletter_admin_menu() {
    add_submenu_page(
        'users.php',
        'Newsletter Subscribers',
        'Newsletter Subscribers',
        'manage_options',
        'newsletter-subscribers',
        'newsletter_subscribers_admin_page'
    );
}
add_action('admin_menu', 'add_newsletter_admin_menu');

// Newsletter subscribers admin page
function newsletter_subscribers_admin_page() {
    if (isset($_GET['export']) && $_GET['export'] === 'csv') {
        export_newsletter_subscribers_csv();
        return;
    }

    $subscribers = get_newsletter_subscribers();
    ?>
    <div class="wrap">
        <h1>Newsletter Subscribers</h1>

        <?php
        // Show MC4WP status
        if (function_exists('mc4wp_get_api')) {
            $lists = mc4wp_get_lists();
            if (!empty($lists)) {
                echo '<div class="notice notice-success inline">';
                echo '<p><strong>MC4WP Integration Active:</strong> Subscribers are automatically synced to Mailchimp. ';
                echo '<a href="' . admin_url('admin.php?page=mailchimp-for-wp') . '">Manage MC4WP Settings</a></p>';
                echo '</div>';
            } else {
                echo '<div class="notice notice-warning inline">';
                echo '<p><strong>MC4WP Setup Required:</strong> Please configure your Mailchimp lists. ';
                echo '<a href="' . admin_url('admin.php?page=mailchimp-for-wp') . '">MC4WP Settings</a></p>';
                echo '</div>';
            }
        } else {
            echo '<div class="notice notice-info inline">';
            echo '<p><strong>MC4WP Plugin:</strong> Install "MC4WP: Mailchimp for WordPress" for automatic Mailchimp sync.</p>';
            echo '</div>';
        }
        ?>

        <div class="tablenav top">
            <div class="alignleft actions">
                <a href="<?php echo admin_url('users.php?page=newsletter-subscribers&export=csv'); ?>" class="button">Export CSV</a>
                <?php if (function_exists('mc4wp_get_api') && !empty(mc4wp_get_lists())) : ?>
                <a href="<?php echo admin_url('admin.php?page=mailchimp-for-wp'); ?>" class="button">MC4WP Settings</a>
                <?php endif; ?>
            </div>
            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo count($subscribers); ?> local subscribers</span>
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Email</th>
                    <th>Name</th>
                    <th>Signup Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($subscribers as $subscriber) : ?>
                <tr>
                    <td><?php echo esc_html($subscriber->user_email); ?></td>
                    <td><?php echo esc_html($subscriber->first_name . ' ' . $subscriber->last_name); ?></td>
                    <td><?php echo $subscriber->signup_date ? date('M j, Y', strtotime($subscriber->signup_date)) : 'N/A'; ?></td>
                    <td>
                        <a href="<?php echo admin_url('user-edit.php?user_id=' . $subscriber->ID); ?>">Edit User</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php
}

// Include newsletter integration
require_once get_template_directory() . '/newsletter-integration.php';

/**
 * Fix WordPress email from address and name
 */
function custom_wp_mail_from($original_email_address) {
    // Use WooCommerce email settings if available
    $wc_from_address = get_option('woocommerce_email_from_address');
    if ($wc_from_address) {
        return $wc_from_address;
    }

    // Fallback to defined constant or admin email
    if (defined('WP_MAIL_FROM')) {
        return WP_MAIL_FROM;
    }

    return get_option('admin_email');
}
add_filter('wp_mail_from', 'custom_wp_mail_from');

function custom_wp_mail_from_name($original_email_from) {
    // Use WooCommerce email settings if available
    $wc_from_name = get_option('woocommerce_email_from_name');
    if ($wc_from_name) {
        return $wc_from_name;
    }

    // Fallback to defined constant or site name
    if (defined('WP_MAIL_FROM_NAME')) {
        return WP_MAIL_FROM_NAME;
    }

    return get_bloginfo('name');
}
add_filter('wp_mail_from_name', 'custom_wp_mail_from_name');

/**
 * Redirect default WordPress login to custom login page
 */
function redirect_to_custom_login() {
    $login_page = home_url('/login/');
    $page = basename($_SERVER['REQUEST_URI']);

    if ($page == "wp-login.php" && $_SERVER['REQUEST_METHOD'] == 'GET') {
        // Allow admin users to access wp-login.php if they have a specific redirect_to parameter
        if (isset($_GET['redirect_to']) && strpos($_GET['redirect_to'], 'wp-admin') !== false) {
            return; // Don't redirect, let them access wp-login.php
        }

        // For all other cases, redirect to custom login page
        wp_redirect($login_page);
        exit;
    }
}
add_action('init', 'redirect_to_custom_login');

/**
 * Redirect after logout
 */
function redirect_after_logout() {
    wp_redirect(home_url('/login/'));
    exit;
}
add_action('wp_logout', 'redirect_after_logout');

/**
 * Update login/register URLs in theme
 */
function custom_login_url($login_url, $redirect, $force_reauth) {
    // If redirect is to admin area, keep the original wp-login.php URL
    if (!empty($redirect) && strpos($redirect, 'wp-admin') !== false) {
        return $login_url;
    }

    // For all other cases, use custom login page
    return home_url('/login/');
}
add_filter('login_url', 'custom_login_url', 10, 3);

function custom_register_url($register_url) {
    return home_url('/register/');
}
add_filter('register_url', 'custom_register_url');

/**
 * Custom Image Sizes
 */
function filmcollectables_image_sizes() {
    add_image_size('competition-thumbnail', 400, 300, true);
    add_image_size('competition-hero', 800, 600, true);
}
add_action('after_setup_theme', 'filmcollectables_image_sizes');

/**
 * Add custom fields to cart items
 */
function add_competition_data_to_cart_item($cart_item_data, $product_id, $variation_id) {
    if (isset($_POST['competition_id'])) {
        $cart_item_data['competition_id'] = sanitize_text_field($_POST['competition_id']);
    }
    if (isset($_POST['answer'])) {
        $cart_item_data['answer'] = sanitize_text_field($_POST['answer']);
    }
    return $cart_item_data;
}
add_filter('woocommerce_add_cart_item_data', 'add_competition_data_to_cart_item', 10, 3);

/**
 * Display custom fields in cart
 */
function display_competition_data_in_cart($item_data, $cart_item) {
    if (isset($cart_item['competition_id'])) {
        $competition = get_post($cart_item['competition_id']);
        if ($competition) {
            $item_data[] = array(
                'key' => 'Competition',
                'value' => $competition->post_title
            );
        }
    }
    if (isset($cart_item['answer'])) {
        $item_data[] = array(
            'key' => 'Answer',
            'value' => $cart_item['answer']
        );
    }
    return $item_data;
}
add_filter('woocommerce_get_item_data', 'display_competition_data_in_cart', 10, 2);

/**
 * Save custom fields to order
 */
function save_competition_data_to_order($item, $cart_item_key, $values, $order) {
    if (isset($values['competition_id'])) {
        $item->add_meta_data('Competition ID', $values['competition_id']);
    }
    if (isset($values['answer'])) {
        $item->add_meta_data('Answer', $values['answer']);
    }
}
add_action('woocommerce_checkout_create_order_line_item', 'save_competition_data_to_order', 10, 4);

/**
 * Fallback menu when no menu is assigned
 */
function filmcollectables_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('competition')) . '">Competitions</a></li>';
    echo '<li><a href="' . esc_url(home_url('/awaiting-draw/')) . '">Awaiting Draw</a></li>';
    echo '<li><a href="' . esc_url(home_url('/completed/')) . '">Completed</a></li>';
    echo '<li><a href="' . esc_url(home_url('/winners/')) . '">Winners</a></li>';
    echo '<li><a href="' . esc_url(home_url('/faq/')) . '">FAQ</a></li>';
    echo '<li><a href="' . esc_url(home_url('/contact/')) . '">Contact</a></li>';
    echo '</ul>';
}

/**
 * Handle adding competition products to cart via AJAX
 */
function add_competition_product_to_cart() {
    // Verify nonce
    if (!check_ajax_referer('competition_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }
    
    $product_id = isset($_POST['competition_id']) ? intval($_POST['competition_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $answer = isset($_POST['answer']) ? intval($_POST['answer']) : null;
    
    if (!$product_id) {
        wp_send_json_error('Invalid product ID');
        return;
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('You must be logged in to enter competitions');
        return;
    }
    
    // Check if user's email is verified
    $user_id = get_current_user_id();
    if (!is_user_email_verified($user_id)) {
        wp_send_json_error('You must verify your email address before entering competitions. Please check your email for the verification link.');
        return;
    }
    
    // Get the product
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error('Product not found');
        return;
    }
    
    // Check if it's a free competition
    $price = $product->get_price();
    if ($price == 0 || $price == '0.00') {
        // Free competitions are limited to one entry per person
        $quantity = 1;
        
        // Check if user already entered this competition
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $existing_entries = get_user_meta($user_id, 'competition_entry_' . $product_id, true);
            if ($existing_entries) {
                wp_send_json_error('You have already entered this free competition. Only one entry per person is allowed.');
                return;
            }
        }
    }
    
    // Check if it's a competition product
    $max_tickets = get_post_meta($product_id, '_competition_max_tickets', true);
    if (!$max_tickets) {
        wp_send_json_error('This is not a competition product');
        return;
    }
    
    // Check competition status using the proper function
    $status = get_competition_status_simple($product_id);
    
    if ($status === 'scheduled') {
        wp_send_json_error('Competition has not started yet');
        return;
    }
    
    if ($status === 'awaiting_draw') {
        wp_send_json_error('Competition has ended - the draw will take place soon!');
        return;
    }
    
    if ($status === 'completed') {
        wp_send_json_error('Competition has ended - winner has been selected');
        return;
    }
    
    if ($status !== 'active') {
        wp_send_json_error('Competition is not active');
        return;
    }
    
    // Check ticket availability
    $sold_tickets = intval(get_post_meta($product_id, '_competition_sold_tickets', true));
    $remaining = $max_tickets - $sold_tickets;
    
    if ($quantity > $remaining) {
        wp_send_json_error('Not enough tickets available. Only ' . $remaining . ' tickets left.');
        return;
    }
    
    // Limit single purchase to 999 tickets (but allow multiple purchases)
    if ($quantity > 999 && $price != 0 && $price != '0.00') {
        wp_send_json_error('Maximum 999 tickets per purchase. You can make multiple purchases for more tickets.');
        return;
    }
    
    // Add to cart with custom data
    $cart_item_data = array(
        'competition_entry' => true,
        'skill_answer' => $answer,
        'tickets_purchased' => $quantity
    );
    
    // Clear any existing error notices
    wc_clear_notices();
    
    $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, 0, array(), $cart_item_data);
    
    if ($cart_item_key) {
        // Update sold tickets count
        $new_sold_count = $sold_tickets + $quantity;
        update_post_meta($product_id, '_competition_sold_tickets', $new_sold_count);

        // Update WooCommerce stock
        $available_tickets = $max_tickets - $new_sold_count;
        update_post_meta($product_id, '_stock', $available_tickets);

        // Update stock status
        $stock_status = $available_tickets > 0 ? 'instock' : 'outofstock';
        update_post_meta($product_id, '_stock_status', $stock_status);

        // Clear WooCommerce product cache
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients($product_id);
        }
        wp_cache_delete($product_id, 'posts');
        wp_cache_delete($product_id, 'post_meta');

        // Mark free competition as entered for this user
        if (($price == 0 || $price == '0.00') && is_user_logged_in()) {
            $user_id = get_current_user_id();
            update_user_meta($user_id, 'competition_entry_' . $product_id, current_time('mysql'));
        }
        
        wp_send_json_success(array(
            'message' => ($price == 0 || $price == '0.00') ? 'Free entry claimed successfully!' : 'Tickets added to basket successfully',
            'cart_url' => wc_get_cart_url(),
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'cart_total' => WC()->cart->get_cart_total()
        ));
    } else {
        $error_message = 'Failed to add tickets to basket.';
        $notices = wc_get_notices('error');
        if (!empty($notices)) {
            $error_message = strip_tags($notices[0]['notice']);
        }
        wp_send_json_error($error_message);
    }
}
add_action('wp_ajax_add_competition_to_cart', 'add_competition_product_to_cart');
add_action('wp_ajax_nopriv_add_competition_to_cart', 'add_competition_product_to_cart');

/**
 * Check competition status via AJAX
 */
function check_competition_status() {
    prevent_ajax_caching();

    $competition_id = isset($_GET['competition_id']) ? intval($_GET['competition_id']) : 0;

    if (!$competition_id) {
        wp_send_json_error('Invalid competition ID');
        return;
    }
    
    $end_date = get_post_meta($competition_id, '_competition_end_date', true);
    $status = get_post_meta($competition_id, 'status', true);
    $current_time = current_time('timestamp');
    
    $is_ended = false;
    if ($end_date && strtotime($end_date) < $current_time) {
        $is_ended = true;
    }
    
    if ($status === 'awaiting_draw' || $status === 'completed') {
        $is_ended = true;
    }
    
    wp_send_json(array(
        'ended' => $is_ended,
        'status' => $status,
        'end_date' => $end_date
    ));
}
add_action('wp_ajax_check_competition_status', 'check_competition_status');
add_action('wp_ajax_nopriv_check_competition_status', 'check_competition_status');

/**
 * Clear competition status when dates are updated
 */
function clear_competition_status_on_date_update($meta_id, $post_id, $meta_key, $meta_value) {
    // Check if we're updating competition dates
    if ($meta_key === '_competition_start_date' || $meta_key === '_competition_end_date') {
        // Delete the stored status to force recalculation
        delete_post_meta($post_id, 'status');
        delete_post_meta($post_id, '_competition_status');
        
        // Clear any transients or caches
        wp_cache_delete($post_id, 'post_meta');
        
        // Optionally, update status based on new dates
        $new_status = get_competition_status_simple($post_id);
        if ($new_status && $new_status !== 'not_competition') {
            update_post_meta($post_id, 'status', $new_status);
        }
    }
}
add_action('updated_post_meta', 'clear_competition_status_on_date_update', 10, 4);
add_action('added_post_meta', 'clear_competition_status_on_date_update', 10, 4);

/**
 * Set UK as default country for checkout
 */
function set_uk_as_default_country($default_location) {
    // Set UK as default country - return string format
    return 'GB';
}
add_filter('woocommerce_customer_default_location', 'set_uk_as_default_country');

/**
 * Set default billing country to UK for new customers
 */
function set_default_billing_country_to_uk($fields) {
    // Set UK as default for billing country
    if (isset($fields['billing_country'])) {
        $fields['billing_country']['default'] = 'GB';
    }
    return $fields;
}
add_filter('woocommerce_checkout_fields', 'set_default_billing_country_to_uk');

/**
 * Force UK as default country in WooCommerce settings
 */
function force_uk_default_country() {
    return 'GB';
}
add_filter('pre_option_woocommerce_default_country', 'force_uk_default_country');

/**
 * Remove wallet credit section and coupon form from checkout
 */
function remove_wallet_credit_and_coupon_from_checkout() {
    // Remove the specific wallet credit checkout field function
    remove_action('woocommerce_checkout_before_order_review', 'add_wallet_credit_checkout_field');

    // Remove wallet credit script
    remove_action('wp_footer', 'wallet_credit_checkout_script');

    // Remove other potential wallet credit hooks
    remove_action('woocommerce_review_order_before_payment', 'display_wallet_credit_option');
    remove_action('woocommerce_checkout_after_customer_details', 'display_wallet_credit_option');

    // Remove the default WooCommerce coupon form from checkout
    remove_action('woocommerce_before_checkout_form', 'woocommerce_checkout_coupon_form', 10);

    // Hide wallet credit and coupon elements with CSS if they still appear
    if (is_checkout()) {
        echo '<style>
            .use-wallet-credit,
            .wallet-credit-section,
            .wallet-credit-option,
            .apply-wallet-credit,
            [class*="wallet-credit"],
            [id*="wallet-credit"],
            #wallet_credit_field,
            .woocommerce-checkout .wallet-credit,
            .woocommerce-form-coupon-toggle,
            .checkout_coupon,
            .woocommerce-form-coupon,
            .coupon-form,
            .checkout-coupon {
                display: none !important;
            }
        </style>';
    }
}
add_action('wp_head', 'remove_wallet_credit_and_coupon_from_checkout');
add_action('init', 'remove_wallet_credit_and_coupon_from_checkout', 5);

// WooCommerce email integration removed temporarily due to loading conflicts

/**
 * Force cache refresh for login page
 */
function force_login_page_refresh() {
    if (is_page('login')) {
        wp_add_inline_script('jquery', 'console.log("Cache busted: ' . time() . '");');
    }
}
add_action('wp_enqueue_scripts', 'force_login_page_refresh');

/**
 * Debug AJAX handler - DISABLED (conflicted with clean login handler)
 */
/*
function debug_custom_login() {
    error_log('Custom login AJAX handler called');
    wp_send_json_success(array(
        'message' => 'Handler is working',
        'redirect_url' => home_url('/my-account/')
    ));
}
add_action('wp_ajax_nopriv_custom_login', 'debug_custom_login');
add_action('wp_ajax_custom_login', 'debug_custom_login');
*/

/**
 * Handle custom login AJAX request - CLEAN VERSION
 */
function handle_custom_login_clean() {
    $username = sanitize_text_field($_POST['log'] ?? '');
    $password = $_POST['pwd'] ?? '';
    $remember = isset($_POST['rememberme']);

    if (empty($username) || empty($password)) {
        wp_send_json_error(array('message' => 'Please enter both username and password'));
    }

    $creds = array(
        'user_login'    => $username,
        'user_password' => $password,
        'remember'      => $remember
    );

    $user = wp_signon($creds, false);

    if (is_wp_error($user)) {
        wp_send_json_error(array('message' => $user->get_error_message()));
    }

    wp_send_json_success(array(
        'message' => 'Login successful',
        'redirect_url' => home_url('/')
    ));
}
add_action('wp_ajax_nopriv_custom_login', 'handle_custom_login_clean');

/**
 * Override WooCommerce login redirect to go to homepage instead of my-account
 */
function custom_woocommerce_login_redirect($redirect, $user) {
    // If user is admin and trying to access admin area, let them
    if (isset($user->roles) && in_array('administrator', $user->roles)) {
        // If redirect contains admin or wp-admin, allow it
        if (strpos($redirect, 'wp-admin') !== false || strpos($redirect, 'admin') !== false) {
            return $redirect;
        }
    }

    // For all other cases (including my-account), redirect to homepage
    return home_url('/');
}
add_filter('woocommerce_login_redirect', 'custom_woocommerce_login_redirect', 10, 2);

/**
 * Override WordPress login redirect to go to homepage instead of admin/my-account
 */
function custom_login_redirect($redirect_to, $request, $user) {
    // If user is admin and specifically trying to access admin area, allow it
    if (isset($user->roles) && in_array('administrator', $user->roles)) {
        // If redirect contains admin or wp-admin, allow it
        if (strpos($redirect_to, 'wp-admin') !== false || strpos($redirect_to, 'admin') !== false) {
            return $redirect_to;
        }
        // If no specific redirect requested, send admin to admin area
        if (empty($request) || $redirect_to === admin_url()) {
            return admin_url();
        }
    }

    // For non-admin users, always redirect to homepage
    return home_url('/');
}
add_filter('login_redirect', 'custom_login_redirect', 10, 3);
add_action('wp_ajax_custom_login', 'handle_custom_login_clean');





